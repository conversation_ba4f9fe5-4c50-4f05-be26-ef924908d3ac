#!/usr/bin/env python3
"""
簡單版蠟筆小新繪圖
避免中文字體問題，直接保存圖片
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np

def draw_shin_chan():
    """畫蠟筆小新"""
    
    # 創建畫布
    fig, ax = plt.subplots(1, 1, figsize=(8, 10))
    ax.set_xlim(0, 8)
    ax.set_ylim(0, 10)
    ax.set_aspect('equal')
    ax.axis('off')  # 隱藏座標軸
    
    # 設置背景色
    fig.patch.set_facecolor('lightblue')
    
    # 1. 畫頭部 (橢圓形)
    head = patches.Ellipse((4, 7.5), 2.5, 3, facecolor='#FFDBAC', edgecolor='black', linewidth=2)
    ax.add_patch(head)
    
    # 2. 畫眉毛 (粗黑線)
    # 左眉毛
    ax.plot([3, 3.5], [8.2, 8.4], 'k-', linewidth=6)
    # 右眉毛  
    ax.plot([4.5, 5], [8.4, 8.2], 'k-', linewidth=6)
    
    # 3. 畫眼睛
    # 左眼 (橢圓)
    left_eye = patches.Ellipse((3.5, 7.8), 0.6, 0.4, facecolor='white', edgecolor='black', linewidth=2)
    ax.add_patch(left_eye)
    # 左眼珠
    left_pupil = patches.Circle((3.5, 7.8), 0.1, facecolor='black')
    ax.add_patch(left_pupil)
    
    # 右眼 (橢圓)
    right_eye = patches.Ellipse((4.5, 7.8), 0.6, 0.4, facecolor='white', edgecolor='black', linewidth=2)
    ax.add_patch(right_eye)
    # 右眼珠
    right_pupil = patches.Circle((4.5, 7.8), 0.1, facecolor='black')
    ax.add_patch(right_pupil)
    
    # 4. 畫鼻子 (小圓點)
    nose = patches.Circle((4, 7.3), 0.08, facecolor='black')
    ax.add_patch(nose)
    
    # 5. 畫嘴巴 (弧線)
    theta = np.linspace(0.2*np.pi, 0.8*np.pi, 30)
    mouth_x = 4 + 0.5 * np.cos(theta)
    mouth_y = 6.8 + 0.2 * np.sin(theta)
    ax.plot(mouth_x, mouth_y, 'k-', linewidth=3)
    
    # 6. 畫頭髮 (幾根豎起的線)
    # 中間的頭髮
    ax.plot([4, 4], [9, 9.5], 'k-', linewidth=4)
    # 左邊的頭髮
    ax.plot([3.5, 3.3], [8.8, 9.3], 'k-', linewidth=3)
    # 右邊的頭髮
    ax.plot([4.5, 4.7], [8.8, 9.3], 'k-', linewidth=3)
    
    # 7. 畫身體 (矩形)
    body = patches.Rectangle((3.2, 4), 1.6, 2.5, facecolor='red', edgecolor='black', linewidth=2)
    ax.add_patch(body)
    
    # 8. 畫手臂
    # 左手臂
    left_arm = patches.Rectangle((2, 5.5), 1.2, 0.6, facecolor='#FFDBAC', edgecolor='black', linewidth=2)
    ax.add_patch(left_arm)
    # 右手臂
    right_arm = patches.Rectangle((4.8, 5.5), 1.2, 0.6, facecolor='#FFDBAC', edgecolor='black', linewidth=2)
    ax.add_patch(right_arm)
    
    # 9. 畫手 (圓形)
    # 左手
    left_hand = patches.Circle((2, 5.8), 0.3, facecolor='#FFDBAC', edgecolor='black', linewidth=2)
    ax.add_patch(left_hand)
    # 右手
    right_hand = patches.Circle((6, 5.8), 0.3, facecolor='#FFDBAC', edgecolor='black', linewidth=2)
    ax.add_patch(right_hand)
    
    # 10. 畫褲子
    pants = patches.Rectangle((3.2, 2), 1.6, 2, facecolor='yellow', edgecolor='black', linewidth=2)
    ax.add_patch(pants)
    
    # 11. 畫腿
    # 左腿
    left_leg = patches.Rectangle((3.2, 0.5), 0.5, 1.5, facecolor='#FFDBAC', edgecolor='black', linewidth=2)
    ax.add_patch(left_leg)
    # 右腿
    right_leg = patches.Rectangle((4.3, 0.5), 0.5, 1.5, facecolor='#FFDBAC', edgecolor='black', linewidth=2)
    ax.add_patch(right_leg)
    
    # 12. 畫鞋子
    # 左鞋
    left_shoe = patches.Ellipse((3.45, 0.3), 0.8, 0.4, facecolor='brown', edgecolor='black', linewidth=2)
    ax.add_patch(left_shoe)
    # 右鞋
    right_shoe = patches.Ellipse((4.55, 0.3), 0.8, 0.4, facecolor='brown', edgecolor='black', linewidth=2)
    ax.add_patch(right_shoe)
    
    # 13. 添加一些細節
    # 在紅色上衣上畫個小圖案
    shirt_detail = patches.Circle((4, 5), 0.15, facecolor='white', edgecolor='black', linewidth=1)
    ax.add_patch(shirt_detail)
    
    # 14. 添加標題 (英文)
    plt.title('Crayon Shin-chan', fontsize=20, fontweight='bold', pad=20)
    
    # 15. 添加一些裝飾星星
    star_x = [1, 7, 1.5, 6.5, 2, 6]
    star_y = [8.5, 8.5, 7, 7, 5.5, 5.5]
    for x, y in zip(star_x, star_y):
        ax.plot(x, y, '*', markersize=12, color='gold')
    
    # 直接保存圖片，不顯示
    plt.savefig('蠟筆小新.png', dpi=300, bbox_inches='tight', facecolor='lightblue')
    print('🎨 蠟筆小新畫好了！已保存為 蠟筆小新.png')
    
    # 關閉圖形，避免顯示
    plt.close()

if __name__ == "__main__":
    print('🎨 開始畫蠟筆小新...')
    draw_shin_chan()
    print('🎉 完成！')
