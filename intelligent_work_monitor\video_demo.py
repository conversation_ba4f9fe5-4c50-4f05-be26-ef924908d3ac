#!/usr/bin/env python3
"""
影片展示程式 - 創建模型預測的影片展示
"""

import cv2
import numpy as np
import pandas as pd
import pickle
import os
from datetime import datetime

try:
    import tensorflow as tf
    from tensorflow.keras.models import load_model
    TF_AVAILABLE = True
except ImportError:
    TF_AVAILABLE = False

class VideoDemo:
    """影片展示類"""
    
    def __init__(self):
        self.model = None
        self.label_encoder = None
        self.scaler = None
        self.config = None
        
    def load_model_components(self):
        """載入模型組件"""
        try:
            if os.path.exists('models/lstm_action_model.h5'):
                self.model = load_model('models/lstm_action_model.h5')
                print("✅ 模型載入成功")
            
            if os.path.exists('models/label_encoder.pkl'):
                with open('models/label_encoder.pkl', 'rb') as f:
                    self.label_encoder = pickle.load(f)
                print("✅ 標籤編碼器載入成功")
            
            if os.path.exists('models/scaler.pkl'):
                with open('models/scaler.pkl', 'rb') as f:
                    self.scaler = pickle.load(f)
                print("✅ 特徵標準化器載入成功")
            
            return True
        except Exception as e:
            print(f"❌ 載入失敗: {e}")
            return False
    
    def create_demo_video(self, csv_file, output_video='results/model_demo_video.mp4'):
        """創建展示影片"""
        print("🎬 創建模型預測展示影片...")
        
        # 載入數據
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        
        # 提取特徵
        key_landmarks = ['左肩膀', '右肩膀', '左手肘', '右手肘', '左手腕', '右手腕', '左手食指', '右手食指']
        feature_columns = []
        for name in key_landmarks:
            x_col = f'{name}_X座標'
            y_col = f'{name}_Y座標'
            z_col = f'{name}_Z座標'
            if all(col in df.columns for col in [x_col, y_col, z_col]):
                feature_columns.extend([x_col, y_col, z_col])
        
        features = df[feature_columns].values
        true_labels = df['標籤'].values
        
        if self.scaler:
            scaled_features = self.scaler.transform(features)
        else:
            scaled_features = features
        
        # 創建序列並預測
        sequence_length = 30
        sequences = []
        for i in range(len(scaled_features) - sequence_length + 1):
            sequences.append(scaled_features[i:i + sequence_length])
        
        sequences = np.array(sequences)
        predictions = self.model.predict(sequences, verbose=0)
        predicted_classes = np.argmax(predictions, axis=1)
        predicted_labels = self.label_encoder.inverse_transform(predicted_classes)
        confidences = np.max(predictions, axis=1)
        
        # 創建影片
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        fps = 10  # 較慢的播放速度以便觀看
        frame_width, frame_height = 1200, 800
        
        out = cv2.VideoWriter(output_video, fourcc, fps, (frame_width, frame_height))
        
        print(f"📹 生成 {len(sequences)} 幀展示影片...")
        
        for i in range(len(sequences)):
            # 創建展示幀
            frame = np.ones((frame_height, frame_width, 3), dtype=np.uint8) * 255
            
            # 計算對應的原始幀索引
            original_frame_idx = i + sequence_length - 1
            
            # 獲取預測信息
            true_label = true_labels[original_frame_idx]
            pred_label = predicted_labels[i]
            confidence = confidences[i]
            is_correct = true_label == pred_label
            
            # 時間信息
            time_str = df.iloc[original_frame_idx]['影片時間'] if '影片時間' in df.columns else f"{original_frame_idx/30:.1f}s"
            
            # 繪製標題
            cv2.putText(frame, "LSTM Action Recognition Demo", (50, 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 0), 3)
            
            # 繪製時間和幀數
            cv2.putText(frame, f"Frame: {original_frame_idx:4d} | Time: {time_str}", (50, 100), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
            
            # 繪製真實動作
            cv2.putText(frame, "True Action:", (50, 150), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
            cv2.putText(frame, true_label, (50, 180), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 100, 0), 2)
            
            # 繪製預測動作
            cv2.putText(frame, "Predicted Action:", (50, 230), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
            
            # 根據正確性選擇顏色
            color = (0, 150, 0) if is_correct else (0, 0, 200)
            cv2.putText(frame, pred_label, (50, 260), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
            
            # 繪製信心度
            cv2.putText(frame, f"Confidence: {confidence:.3f}", (50, 310), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
            
            # 繪製正確性指示
            status = "CORRECT ✓" if is_correct else "INCORRECT ✗"
            status_color = (0, 150, 0) if is_correct else (0, 0, 200)
            cv2.putText(frame, status, (50, 360), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, status_color, 2)
            
            # 繪製信心度條
            bar_x, bar_y = 50, 400
            bar_width, bar_height = 300, 30
            
            # 背景條
            cv2.rectangle(frame, (bar_x, bar_y), (bar_x + bar_width, bar_y + bar_height), (200, 200, 200), -1)
            
            # 信心度條
            conf_width = int(bar_width * confidence)
            conf_color = (0, 150, 0) if confidence > 0.8 else (0, 100, 200) if confidence > 0.5 else (0, 0, 200)
            cv2.rectangle(frame, (bar_x, bar_y), (bar_x + conf_width, bar_y + bar_height), conf_color, -1)
            
            # 信心度數值
            cv2.putText(frame, f"{confidence:.1%}", (bar_x + bar_width + 10, bar_y + 20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
            
            # 繪製統計信息
            total_frames = len(sequences)
            current_progress = (i + 1) / total_frames
            
            cv2.putText(frame, f"Progress: {i+1}/{total_frames} ({current_progress:.1%})", (50, 480), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
            
            # 繪製序列可視化 (簡化版)
            seq_y = 520
            cv2.putText(frame, "Sequence Window (30 frames):", (50, seq_y), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
            
            # 繪製序列窗口
            for j in range(30):
                seq_frame_idx = original_frame_idx - 29 + j
                if seq_frame_idx >= 0:
                    x = 50 + j * 8
                    y = seq_y + 30
                    
                    # 當前幀用紅色標示
                    color = (0, 0, 200) if j == 29 else (100, 100, 100)
                    cv2.rectangle(frame, (x, y), (x + 6, y + 20), color, -1)
            
            # 繪製模型信息
            cv2.putText(frame, "Model: Bidirectional LSTM + Batch Normalization", (50, 650), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
            cv2.putText(frame, "Features: 8 key points × 3 coordinates = 24 dimensions", (50, 680), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
            cv2.putText(frame, "Classes: 20 action types", (50, 710), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
            
            # 寫入幀
            out.write(frame)
            
            # 進度顯示
            if (i + 1) % 100 == 0:
                print(f"  處理進度: {i+1}/{len(sequences)} ({(i+1)/len(sequences)*100:.1f}%)")
        
        out.release()
        print(f"✅ 展示影片已保存: {output_video}")
        
        return output_video

def main():
    """主函數"""
    print("🎬 LSTM模型影片展示程式")
    print("="*50)
    
    demo = VideoDemo()
    
    if not demo.load_model_components():
        print("❌ 模型載入失敗")
        return
    
    csv_file = 'data/1_pose_data_繁體中文_清理版_含時間_含標籤.csv'
    
    if not os.path.exists(csv_file):
        print(f"❌ 找不到數據檔案: {csv_file}")
        return
    
    # 創建展示影片
    video_path = demo.create_demo_video(csv_file)
    
    print("\n🎉 影片展示完成！")
    print(f"📹 展示影片: {video_path}")
    print("💡 您可以播放這個影片來查看模型的實時預測效果")

if __name__ == "__main__":
    main()
