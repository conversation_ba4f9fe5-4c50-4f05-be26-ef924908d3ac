#!/usr/bin/env python3
"""
SOP合規檢查系統
- 3類動作識別 (鎖螺絲/旋轉工件/其他動作)
- 16個螺絲位置檢測
- 狀態追蹤系統
"""

import pandas as pd
import numpy as np
import pickle
import json
import os
from datetime import datetime

class SOPComplianceSystem:
    def __init__(self):
        """初始化SOP合規檢查系統"""
        self.model = None
        self.label_encoder = None
        self.scaler = None
        self.screw_positions = {}
        self.screw_status = {}
        self.load_models()
        self.define_screw_positions()
        self.reset_screw_status()
    
    def load_models(self):
        """載入3類LSTM模型"""
        print('📊 載入3類SOP模型...')
        
        from tensorflow.keras.models import load_model
        self.model = load_model('models/sop_3class_action_model.h5')
        
        with open('models/sop_3class_label_encoder.pkl', 'rb') as f:
            self.label_encoder = pickle.load(f)
        
        with open('models/sop_3class_scaler.pkl', 'rb') as f:
            self.scaler = pickle.load(f)
        
        print('✅ 3類SOP模型載入成功')
        print(f'✅ 支援動作: {list(self.label_encoder.classes_)}')
    
    def define_screw_positions(self):
        """定義16個螺絲位置 - 適應工件旋轉"""
        print('📍 定義16個螺絲位置（適應旋轉工件）...')

        # 工件中心區域 (估計值，可調整)
        self.workpiece_center = {
            'x': 1200,  # 工件中心X座標
            'y': 450    # 工件中心Y座標
        }

        # 定義螺絲相對於工件中心的位置 (極座標方式)
        # 假設螺絲排列為4x4，但使用相對距離和角度
        positions = {}

        # 螺絲分布參數
        base_radius = 120  # 基礎半徑
        angle_step = 90    # 角度步長

        print('🔄 使用相對位置檢測（適應工件旋轉）')
        print(f'   工件中心: ({self.workpiece_center["x"]}, {self.workpiece_center["y"]})')

        # 定義16個螺絲的相對位置
        screw_positions = [
            # 內圈螺絲 (較近工件中心)
            {'radius': 80, 'angle': 45, 'id': 1},   # 右上
            {'radius': 80, 'angle': 135, 'id': 2},  # 左上
            {'radius': 80, 'angle': 225, 'id': 3},  # 左下
            {'radius': 80, 'angle': 315, 'id': 4},  # 右下

            # 中圈螺絲
            {'radius': 120, 'angle': 0, 'id': 5},    # 右
            {'radius': 120, 'angle': 30, 'id': 6},   # 右上
            {'radius': 120, 'angle': 60, 'id': 7},   # 右上
            {'radius': 120, 'angle': 90, 'id': 8},   # 上
            {'radius': 120, 'angle': 120, 'id': 9},  # 左上
            {'radius': 120, 'angle': 150, 'id': 10}, # 左上
            {'radius': 120, 'angle': 180, 'id': 11}, # 左
            {'radius': 120, 'angle': 210, 'id': 12}, # 左下
            {'radius': 120, 'angle': 240, 'id': 13}, # 左下
            {'radius': 120, 'angle': 270, 'id': 14}, # 下
            {'radius': 120, 'angle': 300, 'id': 15}, # 右下
            {'radius': 120, 'angle': 330, 'id': 16}, # 右下
        ]

        # 計算每個螺絲的檢測區域
        import math
        for screw in screw_positions:
            angle_rad = math.radians(screw['angle'])

            # 計算螺絲位置（相對於工件中心）
            rel_x = screw['radius'] * math.cos(angle_rad)
            rel_y = screw['radius'] * math.sin(angle_rad)

            # 轉換為絕對座標
            abs_x = self.workpiece_center['x'] + rel_x
            abs_y = self.workpiece_center['y'] + rel_y

            # 定義檢測區域（圓形區域）
            detection_radius = 40  # 檢測半徑

            positions[screw['id']] = {
                'center_x': abs_x,
                'center_y': abs_y,
                'radius': detection_radius,
                'relative_angle': screw['angle'],
                'relative_radius': screw['radius']
            }

        self.screw_positions = positions
        print(f'✅ 定義了 {len(positions)} 個螺絲位置（圓形檢測區域）')

        # 顯示位置定義
        print('📍 螺絲位置定義（相對工件中心）:')
        for i in range(1, 17):
            pos = positions[i]
            print(f'  位置{i:2d}: 中心({pos["center_x"]:.0f},{pos["center_y"]:.0f}) 半徑{pos["radius"]} 角度{pos["relative_angle"]}°')
    
    def reset_screw_status(self):
        """重置螺絲狀態"""
        self.screw_status = {i: False for i in range(1, 17)}  # 16個螺絲位置
        print('🔄 螺絲狀態已重置')
    
    def detect_hand_position(self, landmarks):
        """檢測手部位置，判斷在哪個螺絲位置（圓形區域檢測）"""
        # 使用右手腕座標作為主要判斷點
        right_wrist_x = landmarks.get('右手腕_X座標', 0)
        right_wrist_y = landmarks.get('右手腕_Y座標', 0)

        # 檢查在哪個螺絲位置的圓形區域內
        import math

        closest_position = None
        min_distance = float('inf')

        for pos_id, pos_info in self.screw_positions.items():
            # 計算手部位置到螺絲中心的距離
            dx = right_wrist_x - pos_info['center_x']
            dy = right_wrist_y - pos_info['center_y']
            distance = math.sqrt(dx*dx + dy*dy)

            # 檢查是否在檢測半徑內
            if distance <= pos_info['radius']:
                # 如果在多個區域內，選擇最近的
                if distance < min_distance:
                    min_distance = distance
                    closest_position = pos_id

        return closest_position  # 返回最近的螺絲位置，或None
    
    def predict_action(self, sequence):
        """預測動作類別"""
        try:
            prediction = self.model.predict(sequence, verbose=0)
            predicted_class = np.argmax(prediction[0])
            confidence = np.max(prediction[0])
            action = self.label_encoder.inverse_transform([predicted_class])[0]
            return action, confidence
        except:
            return "其他動作", 0.0
    
    def update_screw_status(self, position, action, confidence):
        """更新螺絲狀態 - 簡化邏輯"""
        if position and action == "鎖螺絲" and confidence > 0.5:  # 降低信心度門檻
            if not self.screw_status[position]:  # 如果還沒完成
                self.screw_status[position] = True
                return True  # 狀態有更新
        return False  # 狀態沒有更新
    
    def get_compliance_report(self):
        """獲取SOP合規報告"""
        completed = sum(self.screw_status.values())
        total = len(self.screw_status)
        compliance_rate = completed / total * 100
        
        return {
            'completed_screws': completed,
            'total_screws': total,
            'compliance_rate': compliance_rate,
            'status_detail': self.screw_status.copy()
        }
    
    def process_video(self, video_data_path):
        """處理影片進行SOP合規檢查"""
        print(f'🎯 開始SOP合規檢查: {video_data_path}')
        
        # 載入影片姿勢數據
        df = pd.read_csv(video_data_path, encoding='utf-8-sig')
        print(f'✅ 載入影片數據: {len(df)} 幀')
        
        # 提取特徵
        key_landmarks = ['左肩膀', '右肩膀', '左手肘', '右手肘', '左手腕', '右手腕', '左手食指', '右手食指']
        feature_columns = []
        
        for name in key_landmarks:
            x_col = f'{name}_X座標'
            y_col = f'{name}_Y座標'
            z_col = f'{name}_Z座標'
            if all(col in df.columns for col in [x_col, y_col, z_col]):
                feature_columns.extend([x_col, y_col, z_col])
        
        # 標準化特徵
        features = df[feature_columns].values
        scaled_features = self.scaler.transform(features)
        
        # 重置狀態
        self.reset_screw_status()
        
        # 處理結果
        results = []
        sequence_length = 30
        
        print('🔍 開始逐幀分析...')
        
        for i in range(len(scaled_features) - sequence_length + 1):
            # 創建序列
            sequence = scaled_features[i:i + sequence_length]
            sequence = sequence.reshape(1, sequence_length, -1)
            
            # 預測動作
            action, confidence = self.predict_action(sequence)
            
            # 獲取當前幀的手部位置
            frame_idx = i + sequence_length - 1
            landmarks = df.iloc[frame_idx]
            position = self.detect_hand_position(landmarks)
            
            # 更新螺絲狀態
            status_updated = self.update_screw_status(position, action, confidence)
            
            # 記錄結果
            result = {
                'frame': frame_idx,
                'time': landmarks.get('影片時間', f'{frame_idx/30:.2f}s'),
                'action': action,
                'confidence': confidence,
                'position': position,
                'status_updated': status_updated
            }
            
            # 添加當前螺絲狀態
            result.update({f'screw_{i}': self.screw_status[i] for i in range(1, 17)})
            
            results.append(result)
            
            # 進度顯示
            if (i + 1) % 200 == 0:
                progress = (i + 1) / (len(scaled_features) - sequence_length + 1) * 100
                completed = sum(self.screw_status.values())
                print(f'  進度: {progress:.1f}% | 已完成螺絲: {completed}/16')
        
        # 轉換為DataFrame
        df_results = pd.DataFrame(results)
        
        # 生成報告
        compliance_report = self.get_compliance_report()
        
        # 保存結果
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f'2-pose/sop_compliance_report_{timestamp}.csv'
        df_results.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        # 保存合規報告
        report_file = f'2-pose/sop_compliance_summary_{timestamp}.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(compliance_report, f, ensure_ascii=False, indent=2)
        
        print(f'\n✅ SOP合規檢查完成！')
        print(f'📁 詳細結果: {output_file}')
        print(f'📁 合規報告: {report_file}')
        
        return df_results, compliance_report
    
    def print_compliance_summary(self, compliance_report):
        """打印合規摘要"""
        print('\n📊 SOP合規檢查摘要:')
        print('='*50)
        
        completed = compliance_report['completed_screws']
        total = compliance_report['total_screws']
        rate = compliance_report['compliance_rate']
        
        print(f'✅ 已完成螺絲: {completed}/{total}')
        print(f'📈 合規率: {rate:.1f}%')
        
        print('\n🔩 螺絲完成狀態:')
        status = compliance_report['status_detail']
        
        # 4x4網格顯示
        for row in range(4):
            row_status = []
            for col in range(4):
                pos = row * 4 + col + 1
                symbol = '✅' if status[pos] else '❌'
                row_status.append(f'{pos:2d}{symbol}')
            print('  ' + '  '.join(row_status))
        
        print('\n💡 說明:')
        print('  ✅ = 螺絲已完成')
        print('  ❌ = 螺絲未完成')

def main():
    """主函數"""
    print('🚀 SOP合規檢查系統')
    print('='*50)
    
    # 創建SOP系統
    sop_system = SOPComplianceSystem()
    
    # 處理影片2
    video_data_path = '2-pose/2_pose_data_繁體中文_清理版_含時間.csv'
    
    if not os.path.exists(video_data_path):
        print(f'❌ 找不到影片數據: {video_data_path}')
        return
    
    # 執行SOP合規檢查
    results, compliance_report = sop_system.process_video(video_data_path)
    
    # 顯示結果
    sop_system.print_compliance_summary(compliance_report)
    
    print('\n🎉 SOP合規檢查系統運行完成！')

if __name__ == "__main__":
    main()
