#!/usr/bin/env python3
"""
SOP合規檢查系統
- 3類動作識別 (鎖螺絲/旋轉工件/其他動作)
- 16個螺絲位置檢測
- 狀態追蹤系統
"""

import pandas as pd
import numpy as np
import pickle
import json
import os
from datetime import datetime

class SOPComplianceSystem:
    def __init__(self):
        """初始化SOP合規檢查系統"""
        self.model = None
        self.label_encoder = None
        self.scaler = None
        self.screw_positions = {}
        self.screw_status = {}
        self.load_models()
        self.define_screw_positions()
        self.reset_screw_status()
    
    def load_models(self):
        """載入3類LSTM模型"""
        print('📊 載入3類SOP模型...')
        
        from tensorflow.keras.models import load_model
        self.model = load_model('models/sop_3class_action_model.h5')
        
        with open('models/sop_3class_label_encoder.pkl', 'rb') as f:
            self.label_encoder = pickle.load(f)
        
        with open('models/sop_3class_scaler.pkl', 'rb') as f:
            self.scaler = pickle.load(f)
        
        print('✅ 3類SOP模型載入成功')
        print(f'✅ 支援動作: {list(self.label_encoder.classes_)}')
    
    def define_screw_positions(self):
        """定義16個螺絲孔位置 - 時間軸標記版本"""
        print('📍 載入時間軸標記的16個螺絲孔位置...')

        # 根據實際CSV數據修正時間軸標記
        positions = {
            1: {
                'x_min': 1275,
                'x_max': 1323,
                'y_min': 593,
                'y_max': 644,
                'center': (1299, 618),
                'marked_at_time': '00:05.84',  # 修正後的時間
                'marked_at_frame': 108
            },
            2: {
                'x_min': 1418,
                'x_max': 1464,
                'y_min': 528,
                'y_max': 570,
                'center': (1441, 549),
                'marked_at_time': '00:09.57',  # 修正後的時間
                'marked_at_frame': 188
            },
            3: {
                'x_min': 1397,
                'x_max': 1453,
                'y_min': 537,
                'y_max': 578,
                'center': (1425, 557),
                'marked_at_time': '00:12.61',  # 修正後的時間
                'marked_at_frame': 249
            },
            4: {
                'x_min': 1346,
                'x_max': 1406,
                'y_min': 554,
                'y_max': 583,
                'center': (1376, 568),
                'marked_at_time': '00:15.65',  # 修正後的時間
                'marked_at_frame': 310
            },
            5: {
                'x_min': 1430,
                'x_max': 1498,
                'y_min': 528,
                'y_max': 572,
                'center': (1464, 550),
                'marked_at_time': '00:18.59',  # 修正後的時間
                'marked_at_frame': 368
            },
            6: {
                'x_min': 1445,
                'x_max': 1502,
                'y_min': 535,
                'y_max': 578,
                'center': (1473, 556),
                'marked_at_time': '00:22.02',  # 修正後的時間
                'marked_at_frame': 436
            },
            7: {
                'x_min': 1269,
                'x_max': 1349,
                'y_min': 612,
                'y_max': 663,
                'center': (1309, 637),
                'marked_at_time': '00:21',
                'marked_at_frame': 635
            },
            8: {
                'x_min': 1190,
                'x_max': 1261,
                'y_min': 663,
                'y_max': 705,
                'center': (1225, 684),
                'marked_at_time': '00:24',
                'marked_at_frame': 722
            },
            9: {
                'x_min': 1115,
                'x_max': 1181,
                'y_min': 643,
                'y_max': 698,
                'center': (1148, 670),
                'marked_at_time': '00:26',
                'marked_at_frame': 780
            },
            10: {
                'x_min': 1002,
                'x_max': 1050,
                'y_min': 676,
                'y_max': 730,
                'center': (1026, 703),
                'marked_at_time': '00:27',
                'marked_at_frame': 838
            },
            11: {
                'x_min': 1114,
                'x_max': 1208,
                'y_min': 521,
                'y_max': 564,
                'center': (1161, 542),
                'marked_at_time': '00:31',
                'marked_at_frame': 936
            },
            12: {
                'x_min': 890,
                'x_max': 978,
                'y_min': 630,
                'y_max': 680,
                'center': (934, 655),
                'marked_at_time': '00:33',
                'marked_at_frame': 1008
            },
            13: {
                'x_min': 1059,
                'x_max': 1118,
                'y_min': 544,
                'y_max': 590,
                'center': (1088, 567),
                'marked_at_time': '00:35',
                'marked_at_frame': 1076
            },
            14: {
                'x_min': 1026,
                'x_max': 1090,
                'y_min': 583,
                'y_max': 621,
                'center': (1058, 602),
                'marked_at_time': '00:38',
                'marked_at_frame': 1163
            },
            15: {
                'x_min': 1014,
                'x_max': 1088,
                'y_min': 589,
                'y_max': 637,
                'center': (1051, 613),
                'marked_at_time': '00:42',
                'marked_at_frame': 1265
            },
            16: {
                'x_min': 1078,
                'x_max': 1158,
                'y_min': 562,
                'y_max': 621,
                'center': (1118, 591),
                'marked_at_time': '00:45',
                'marked_at_frame': 1363
            }
        }

        self.screw_positions = positions
        print(f'✅ 載入了 {len(positions)} 個時間軸標記的螺絲孔位置')

        # 顯示標記時間資訊
        print('📍 螺絲孔標記時間:')
        for i in range(1, 17):
            if i in positions:
                pos = positions[i]
                print(f'  螺絲孔{i:2d}: {pos["marked_at_time"]} 中心({pos["center"][0]}, {pos["center"][1]})')
    
    def reset_screw_status(self):
        """重置螺絲狀態"""
        self.screw_status = {i: False for i in range(1, 17)}  # 16個螺絲位置
        print('🔄 螺絲狀態已重置')
    
    def detect_hand_position(self, landmarks):
        """檢測手部位置，判斷在哪個螺絲位置（矩形區域檢測）"""
        # 使用右手腕座標作為主要判斷點
        right_wrist_x = landmarks.get('右手腕_X座標', 0)
        right_wrist_y = landmarks.get('右手腕_Y座標', 0)

        # 檢查在哪個螺絲位置的矩形區域內
        for pos_id, pos_info in self.screw_positions.items():
            # 檢查是否在矩形區域內
            if (pos_info['x_min'] <= right_wrist_x <= pos_info['x_max'] and
                pos_info['y_min'] <= right_wrist_y <= pos_info['y_max']):
                return pos_id

        return None  # 沒有在任何螺絲位置區域內
    
    def predict_action(self, sequence):
        """預測動作類別"""
        try:
            prediction = self.model.predict(sequence, verbose=0)
            predicted_class = np.argmax(prediction[0])
            confidence = np.max(prediction[0])
            action = self.label_encoder.inverse_transform([predicted_class])[0]
            return action, confidence
        except:
            return "其他動作", 0.0
    
    def update_screw_status(self, position, action, confidence):
        """更新螺絲狀態 - 簡化邏輯"""
        if position and action == "鎖螺絲" and confidence > 0.5:  # 降低信心度門檻
            if not self.screw_status[position]:  # 如果還沒完成
                self.screw_status[position] = True
                return True  # 狀態有更新
        return False  # 狀態沒有更新
    
    def get_compliance_report(self):
        """獲取SOP合規報告"""
        completed = sum(self.screw_status.values())
        total = len(self.screw_status)
        compliance_rate = completed / total * 100
        
        return {
            'completed_screws': completed,
            'total_screws': total,
            'compliance_rate': compliance_rate,
            'status_detail': self.screw_status.copy()
        }
    
    def process_video(self, video_data_path):
        """處理影片進行SOP合規檢查"""
        print(f'🎯 開始SOP合規檢查: {video_data_path}')
        
        # 載入影片姿勢數據
        df = pd.read_csv(video_data_path, encoding='utf-8-sig')
        print(f'✅ 載入影片數據: {len(df)} 幀')
        
        # 提取特徵
        key_landmarks = ['左肩膀', '右肩膀', '左手肘', '右手肘', '左手腕', '右手腕', '左手食指', '右手食指']
        feature_columns = []
        
        for name in key_landmarks:
            x_col = f'{name}_X座標'
            y_col = f'{name}_Y座標'
            z_col = f'{name}_Z座標'
            if all(col in df.columns for col in [x_col, y_col, z_col]):
                feature_columns.extend([x_col, y_col, z_col])
        
        # 標準化特徵
        features = df[feature_columns].values
        scaled_features = self.scaler.transform(features)
        
        # 重置狀態
        self.reset_screw_status()
        
        # 處理結果
        results = []
        sequence_length = 30
        
        print('🔍 開始逐幀分析...')
        
        for i in range(len(scaled_features) - sequence_length + 1):
            # 創建序列
            sequence = scaled_features[i:i + sequence_length]
            sequence = sequence.reshape(1, sequence_length, -1)
            
            # 預測動作
            action, confidence = self.predict_action(sequence)
            
            # 獲取當前幀的手部位置
            frame_idx = i + sequence_length - 1
            landmarks = df.iloc[frame_idx]
            position = self.detect_hand_position(landmarks)
            
            # 更新螺絲狀態
            status_updated = self.update_screw_status(position, action, confidence)
            
            # 記錄結果
            result = {
                'frame': frame_idx,
                'time': landmarks.get('影片時間', f'{frame_idx/30:.2f}s'),
                'action': action,
                'confidence': confidence,
                'position': position,
                'status_updated': status_updated
            }
            
            # 添加當前螺絲狀態
            result.update({f'screw_{i}': self.screw_status[i] for i in range(1, 17)})
            
            results.append(result)
            
            # 進度顯示
            if (i + 1) % 200 == 0:
                progress = (i + 1) / (len(scaled_features) - sequence_length + 1) * 100
                completed = sum(self.screw_status.values())
                print(f'  進度: {progress:.1f}% | 已完成螺絲: {completed}/16')
        
        # 轉換為DataFrame
        df_results = pd.DataFrame(results)
        
        # 生成報告
        compliance_report = self.get_compliance_report()
        
        # 保存結果
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f'2-pose/sop_compliance_report_{timestamp}.csv'
        df_results.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        # 保存合規報告
        report_file = f'2-pose/sop_compliance_summary_{timestamp}.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(compliance_report, f, ensure_ascii=False, indent=2)
        
        print(f'\n✅ SOP合規檢查完成！')
        print(f'📁 詳細結果: {output_file}')
        print(f'📁 合規報告: {report_file}')
        
        return df_results, compliance_report
    
    def print_compliance_summary(self, compliance_report):
        """打印合規摘要"""
        print('\n📊 SOP合規檢查摘要:')
        print('='*50)
        
        completed = compliance_report['completed_screws']
        total = compliance_report['total_screws']
        rate = compliance_report['compliance_rate']
        
        print(f'✅ 已完成螺絲: {completed}/{total}')
        print(f'📈 合規率: {rate:.1f}%')
        
        print('\n🔩 螺絲完成狀態:')
        status = compliance_report['status_detail']
        
        # 4x4網格顯示
        for row in range(4):
            row_status = []
            for col in range(4):
                pos = row * 4 + col + 1
                symbol = '✅' if status[pos] else '❌'
                row_status.append(f'{pos:2d}{symbol}')
            print('  ' + '  '.join(row_status))
        
        print('\n💡 說明:')
        print('  ✅ = 螺絲已完成')
        print('  ❌ = 螺絲未完成')

def main():
    """主函數"""
    print('🚀 SOP合規檢查系統')
    print('='*50)
    
    # 創建SOP系統
    sop_system = SOPComplianceSystem()
    
    # 處理影片2
    video_data_path = '2-pose/2_pose_data_繁體中文_清理版_含時間.csv'
    
    if not os.path.exists(video_data_path):
        print(f'❌ 找不到影片數據: {video_data_path}')
        return
    
    # 執行SOP合規檢查
    results, compliance_report = sop_system.process_video(video_data_path)
    
    # 顯示結果
    sop_system.print_compliance_summary(compliance_report)
    
    print('\n🎉 SOP合規檢查系統運行完成！')

if __name__ == "__main__":
    main()
