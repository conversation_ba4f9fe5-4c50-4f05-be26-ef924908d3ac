#!/usr/bin/env python3
"""
GPU加速LSTM訓練器
專門用於訓練螺絲作業動作識別模型，支持GPU加速和超參數優化
"""

import os
import numpy as np
import pandas as pd
from datetime import datetime
import json
import argparse

# GPU配置
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'  # 減少TensorFlow日誌

try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import LSTM, Dense, Dropout, BatchNormalization, Bidirectional
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
    from tensorflow.keras.utils import to_categorical
    from sklearn.preprocessing import LabelEncoder, StandardScaler
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import classification_report, confusion_matrix
    import matplotlib.pyplot as plt
    import seaborn as sns
    
    TF_AVAILABLE = True
    print("✅ TensorFlow載入成功")
    
    # 配置GPU
    gpus = tf.config.experimental.list_physical_devices('GPU')
    if gpus:
        try:
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
            print(f"🚀 檢測到 {len(gpus)} 個GPU，已啟用GPU加速")
        except RuntimeError as e:
            print(f"⚠️ GPU配置錯誤: {e}")
    else:
        print("⚠️ 未檢測到GPU，將使用CPU訓練")
        
except ImportError as e:
    print(f"❌ TensorFlow載入失敗: {e}")
    TF_AVAILABLE = False

class GPULSTMTrainer:
    """GPU加速LSTM訓練器"""
    
    def __init__(self, config=None):
        self.config = config or self.get_default_config()
        self.label_encoder = LabelEncoder()
        self.scaler = StandardScaler()
        self.model = None
        self.history = None
        
        # 設置中文字體
        plt.rcParams['font.sans-serif'] = ['Microsoft JhengHei', 'SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        
    def get_default_config(self):
        """獲取默認配置"""
        return {
            'sequence_length': 30,
            'hidden_size': 128,
            'num_layers': 2,
            'dropout': 0.3,
            'learning_rate': 0.001,
            'batch_size': 32,
            'epochs': 100,
            'validation_split': 0.2,
            'early_stopping_patience': 15,
            'reduce_lr_patience': 10,
            'use_bidirectional': True,
            'use_attention': False,
            'use_batch_norm': True
        }
    
    def load_and_preprocess_data(self, csv_file):
        """載入並預處理數據"""
        print(f"📊 載入數據: {csv_file}")
        
        # 載入CSV
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        print(f"✅ 成功載入 {len(df)} 行數據")
        
        # MediaPipe 33個關鍵點
        landmark_names = [
            '鼻子', '左眼內角', '左眼中心', '左眼外角', '右眼內角', '右眼中心', '右眼外角',
            '左耳', '右耳', '嘴巴左角', '嘴巴右角', '左肩膀', '右肩膀', '左手肘', '右手肘',
            '左手腕', '右手腕', '左手小指', '右手小指', '左手食指', '右手食指', '左手拇指', '右手拇指',
            '左臀部', '右臀部', '左膝蓋', '右膝蓋', '左腳踝', '右腳踝', '左腳跟', '右腳跟', '左腳趾', '右腳趾'
        ]
        
        # 提取特徵 (只使用關鍵的8個點)
        key_landmarks = ['左肩膀', '右肩膀', '左手肘', '右手肘', '左手腕', '右手腕', '左手食指', '右手食指']
        
        feature_columns = []
        for name in key_landmarks:
            x_col = f'{name}_X座標'
            y_col = f'{name}_Y座標'
            z_col = f'{name}_Z座標'
            
            if x_col in df.columns and y_col in df.columns and z_col in df.columns:
                feature_columns.extend([x_col, y_col, z_col])
        
        print(f"📋 使用特徵: {len(feature_columns)} 個 ({len(key_landmarks)} 個關鍵點 × 3座標)")
        
        # 提取特徵和標籤
        features = df[feature_columns].values
        labels = df['標籤'].values
        
        # 標籤編碼
        encoded_labels = self.label_encoder.fit_transform(labels)
        
        # 特徵標準化
        scaled_features = self.scaler.fit_transform(features)
        
        print(f"🏷️ 動作類別: {len(self.label_encoder.classes_)} 種")
        for i, label in enumerate(self.label_encoder.classes_):
            count = np.sum(encoded_labels == i)
            print(f"  {label}: {count} 個樣本")
        
        return scaled_features, encoded_labels
    
    def create_sequences(self, features, labels):
        """創建時序序列"""
        print(f"🔄 創建時序序列 (長度: {self.config['sequence_length']})")
        
        sequences = []
        sequence_labels = []
        
        # 滑動窗口創建序列
        for i in range(len(features) - self.config['sequence_length'] + 1):
            sequence = features[i:i + self.config['sequence_length']]
            label = labels[i + self.config['sequence_length'] - 1]  # 使用序列最後一幀的標籤
            
            sequences.append(sequence)
            sequence_labels.append(label)
        
        sequences = np.array(sequences)
        sequence_labels = np.array(sequence_labels)
        
        print(f"✅ 生成 {len(sequences)} 個序列")
        
        return sequences, sequence_labels
    
    def build_model(self, input_shape, num_classes):
        """構建LSTM模型"""
        print("🏗️ 構建LSTM模型...")
        
        model = Sequential()
        
        # 第一層LSTM
        if self.config['use_bidirectional']:
            model.add(Bidirectional(
                LSTM(self.config['hidden_size'], 
                     return_sequences=True if self.config['num_layers'] > 1 else False,
                     dropout=self.config['dropout'],
                     recurrent_dropout=self.config['dropout']),
                input_shape=input_shape
            ))
        else:
            model.add(LSTM(
                self.config['hidden_size'],
                return_sequences=True if self.config['num_layers'] > 1 else False,
                dropout=self.config['dropout'],
                recurrent_dropout=self.config['dropout'],
                input_shape=input_shape
            ))
        
        if self.config['use_batch_norm']:
            model.add(BatchNormalization())
        
        # 額外的LSTM層
        for i in range(1, self.config['num_layers']):
            return_sequences = i < self.config['num_layers'] - 1
            
            if self.config['use_bidirectional']:
                model.add(Bidirectional(
                    LSTM(self.config['hidden_size'] // (i + 1),
                         return_sequences=return_sequences,
                         dropout=self.config['dropout'],
                         recurrent_dropout=self.config['dropout'])
                ))
            else:
                model.add(LSTM(
                    self.config['hidden_size'] // (i + 1),
                    return_sequences=return_sequences,
                    dropout=self.config['dropout'],
                    recurrent_dropout=self.config['dropout']
                ))
            
            if self.config['use_batch_norm']:
                model.add(BatchNormalization())
        
        # 分類層
        model.add(Dense(self.config['hidden_size'] // 2, activation='relu'))
        model.add(Dropout(self.config['dropout']))
        
        if self.config['use_batch_norm']:
            model.add(BatchNormalization())
        
        model.add(Dense(num_classes, activation='softmax'))
        
        # 編譯模型
        optimizer = Adam(learning_rate=self.config['learning_rate'])
        model.compile(
            optimizer=optimizer,
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        print("✅ 模型構建完成")
        model.summary()
        
        return model
    
    def train(self, csv_file, save_path='models/lstm_action_model.h5'):
        """訓練模型"""
        if not TF_AVAILABLE:
            print("❌ TensorFlow不可用，無法訓練")
            return False
        
        print("🚀 開始GPU加速LSTM訓練...")
        print("="*60)
        
        # 載入和預處理數據
        features, labels = self.load_and_preprocess_data(csv_file)
        
        # 創建序列
        sequences, sequence_labels = self.create_sequences(features, labels)
        
        # 轉換標籤為one-hot編碼
        num_classes = len(self.label_encoder.classes_)
        categorical_labels = to_categorical(sequence_labels, num_classes)
        
        # 分割數據
        X_train, X_test, y_train, y_test = train_test_split(
            sequences, categorical_labels, 
            test_size=self.config['validation_split'], 
            random_state=42,
            stratify=sequence_labels
        )
        
        print(f"📊 訓練集: {len(X_train)} 個序列")
        print(f"📊 測試集: {len(X_test)} 個序列")
        
        # 構建模型
        input_shape = (self.config['sequence_length'], sequences.shape[2])
        self.model = self.build_model(input_shape, num_classes)
        
        # 設置回調函數
        callbacks = [
            EarlyStopping(
                monitor='val_accuracy',
                patience=self.config['early_stopping_patience'],
                restore_best_weights=True,
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.5,
                patience=self.config['reduce_lr_patience'],
                min_lr=1e-7,
                verbose=1
            ),
            ModelCheckpoint(
                save_path,
                monitor='val_accuracy',
                save_best_only=True,
                verbose=1
            )
        ]
        
        # 開始訓練
        print("🎯 開始訓練...")
        start_time = datetime.now()
        
        self.history = self.model.fit(
            X_train, y_train,
            batch_size=self.config['batch_size'],
            epochs=self.config['epochs'],
            validation_data=(X_test, y_test),
            callbacks=callbacks,
            verbose=1
        )
        
        end_time = datetime.now()
        training_time = end_time - start_time
        
        print(f"⏱️ 訓練時間: {training_time}")
        
        # 評估模型
        self.evaluate_model(X_test, y_test)
        
        # 保存相關檔案
        self.save_training_artifacts(save_path)
        
        return True
    
    def evaluate_model(self, X_test, y_test):
        """評估模型性能"""
        print("\n📊 模型評估...")
        
        # 預測
        y_pred = self.model.predict(X_test)
        y_pred_classes = np.argmax(y_pred, axis=1)
        y_true_classes = np.argmax(y_test, axis=1)
        
        # 計算準確率
        accuracy = np.mean(y_pred_classes == y_true_classes)
        print(f"🎯 測試準確率: {accuracy:.4f} ({accuracy*100:.2f}%)")
        
        # 分類報告
        print("\n📋 詳細分類報告:")
        report = classification_report(
            y_true_classes, y_pred_classes,
            target_names=self.label_encoder.classes_,
            zero_division=0
        )
        print(report)
        
        # 混淆矩陣
        self.plot_confusion_matrix(y_true_classes, y_pred_classes)
        
        return accuracy
    
    def plot_confusion_matrix(self, y_true, y_pred):
        """繪製混淆矩陣"""
        try:
            cm = confusion_matrix(y_true, y_pred)
            
            plt.figure(figsize=(12, 10))
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                       xticklabels=self.label_encoder.classes_,
                       yticklabels=self.label_encoder.classes_)
            plt.title('混淆矩陣')
            plt.xlabel('預測標籤')
            plt.ylabel('真實標籤')
            plt.xticks(rotation=45)
            plt.yticks(rotation=0)
            plt.tight_layout()
            plt.savefig('results/confusion_matrix.png', dpi=300, bbox_inches='tight')
            plt.close()
            
            print("📈 混淆矩陣已保存: results/confusion_matrix.png")
        except Exception as e:
            print(f"⚠️ 混淆矩陣繪製失敗: {e}")
    
    def plot_training_history(self):
        """繪製訓練歷史"""
        if self.history is None:
            return
        
        try:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
            
            # 準確率
            ax1.plot(self.history.history['accuracy'], label='訓練準確率')
            ax1.plot(self.history.history['val_accuracy'], label='驗證準確率')
            ax1.set_title('模型準確率')
            ax1.set_xlabel('Epoch')
            ax1.set_ylabel('準確率')
            ax1.legend()
            ax1.grid(True)
            
            # 損失
            ax2.plot(self.history.history['loss'], label='訓練損失')
            ax2.plot(self.history.history['val_loss'], label='驗證損失')
            ax2.set_title('模型損失')
            ax2.set_xlabel('Epoch')
            ax2.set_ylabel('損失')
            ax2.legend()
            ax2.grid(True)
            
            plt.tight_layout()
            plt.savefig('results/training_history.png', dpi=300, bbox_inches='tight')
            plt.close()
            
            print("📈 訓練歷史已保存: results/training_history.png")
        except Exception as e:
            print(f"⚠️ 訓練歷史繪製失敗: {e}")
    
    def save_training_artifacts(self, model_path):
        """保存訓練相關檔案"""
        print("\n💾 保存訓練檔案...")
        
        # 創建目錄
        os.makedirs('models', exist_ok=True)
        os.makedirs('results', exist_ok=True)
        
        # 保存標籤編碼器和標準化器
        import pickle
        
        with open('models/label_encoder.pkl', 'wb') as f:
            pickle.dump(self.label_encoder, f)
        
        with open('models/scaler.pkl', 'wb') as f:
            pickle.dump(self.scaler, f)
        
        # 保存配置
        with open('models/config.json', 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)
        
        # 保存訓練歷史
        if self.history:
            self.plot_training_history()
        
        print("✅ 所有檔案保存完成")

def main():
    """主函數"""
    parser = argparse.ArgumentParser(description='GPU加速LSTM訓練器')
    parser.add_argument('--data', type=str, 
                       default='data/1_pose_data_繁體中文_清理版_含時間_含標籤.csv',
                       help='訓練數據CSV檔案路徑')
    parser.add_argument('--output', type=str, 
                       default='models/lstm_action_model.h5',
                       help='模型輸出路徑')
    parser.add_argument('--epochs', type=int, default=100,
                       help='訓練輪數')
    parser.add_argument('--batch-size', type=int, default=32,
                       help='批次大小')
    parser.add_argument('--hidden-size', type=int, default=128,
                       help='隱藏層大小')
    parser.add_argument('--sequence-length', type=int, default=30,
                       help='序列長度')
    
    args = parser.parse_args()
    
    # 配置
    config = {
        'sequence_length': args.sequence_length,
        'hidden_size': args.hidden_size,
        'num_layers': 2,
        'dropout': 0.3,
        'learning_rate': 0.001,
        'batch_size': args.batch_size,
        'epochs': args.epochs,
        'validation_split': 0.2,
        'early_stopping_patience': 15,
        'reduce_lr_patience': 10,
        'use_bidirectional': True,
        'use_attention': False,
        'use_batch_norm': True
    }
    
    # 創建訓練器
    trainer = GPULSTMTrainer(config)
    
    # 開始訓練
    success = trainer.train(args.data, args.output)
    
    if success:
        print("\n🎉 訓練完成！")
        print("📁 生成的檔案:")
        print("  - models/lstm_action_model.h5 (訓練好的模型)")
        print("  - models/label_encoder.pkl (標籤編碼器)")
        print("  - models/scaler.pkl (特徵標準化器)")
        print("  - models/config.json (模型配置)")
        print("  - results/confusion_matrix.png (混淆矩陣)")
        print("  - results/training_history.png (訓練歷史)")
    else:
        print("\n❌ 訓練失敗！")

if __name__ == "__main__":
    main()
