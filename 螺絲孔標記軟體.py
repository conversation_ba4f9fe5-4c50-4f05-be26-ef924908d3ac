#!/usr/bin/env python3
"""
螺絲孔位置標記軟體
功能：圈選16個螺絲孔位置，標記編號，匯出配置檔案
"""

import cv2
import json
import numpy as np
import os
from datetime import datetime

class ScrewHoleMarker:
    def __init__(self):
        self.positions = {}
        self.image = None
        self.display_image = None
        self.drawing = False
        self.start_point = None
        self.current_hole = 1
        self.colors = [
            (0, 255, 0),    # 綠色
            (255, 0, 0),    # 藍色
            (0, 0, 255),    # 紅色
            (255, 255, 0),  # 青色
            (255, 0, 255),  # 洋紅
            (0, 255, 255),  # 黃色
        ]
        
    def load_video_frame(self, video_path, frame_number=100):
        """載入影片幀"""
        print(f'📹 載入影片: {video_path}')
        print(f'🖼️ 載入第 {frame_number} 幀...')
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print('❌ 無法開啟影片檔案')
            return False
            
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        print(f'📊 影片總幀數: {total_frames}')
        
        if frame_number >= total_frames:
            frame_number = total_frames // 2
            print(f'⚠️ 幀數超出範圍，使用中間幀: {frame_number}')
        
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
        ret, frame = cap.read()
        cap.release()
        
        if ret:
            self.image = frame.copy()
            self.display_image = frame.copy()
            print('✅ 影片幀載入成功')
            return True
        else:
            print('❌ 無法讀取影片幀')
            return False
    
    def mouse_callback(self, event, x, y, flags, param):
        """滑鼠事件處理"""
        if self.current_hole > 16:
            return
            
        if event == cv2.EVENT_LBUTTONDOWN:
            self.drawing = True
            self.start_point = (x, y)
            
        elif event == cv2.EVENT_MOUSEMOVE and self.drawing:
            # 即時顯示拖拽框
            temp_image = self.display_image.copy()
            color = self.colors[self.current_hole % len(self.colors)]
            cv2.rectangle(temp_image, self.start_point, (x, y), color, 2)
            
            # 顯示螺絲孔編號
            text = f'螺絲孔 {self.current_hole}'
            cv2.putText(temp_image, text, 
                       (self.start_point[0], self.start_point[1] - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
            
            cv2.imshow('螺絲孔標記軟體', temp_image)
            
        elif event == cv2.EVENT_LBUTTONUP and self.drawing:
            self.drawing = False
            if abs(x - self.start_point[0]) > 10 and abs(y - self.start_point[1]) > 10:
                self.add_screw_hole(self.start_point, (x, y))
    
    def add_screw_hole(self, start_point, end_point):
        """添加螺絲孔位置"""
        x1, y1 = start_point
        x2, y2 = end_point
        
        # 確保座標順序正確
        x_min, x_max = min(x1, x2), max(x1, x2)
        y_min, y_max = min(y1, y2), max(y1, y2)
        
        # 計算中心點和尺寸
        center_x = (x_min + x_max) / 2
        center_y = (y_min + y_max) / 2
        width = x_max - x_min
        height = y_max - y_min
        
        # 儲存螺絲孔資訊
        self.positions[self.current_hole] = {
            'id': self.current_hole,
            'x_min': int(x_min),
            'x_max': int(x_max),
            'y_min': int(y_min),
            'y_max': int(y_max),
            'center_x': int(center_x),
            'center_y': int(center_y),
            'width': int(width),
            'height': int(height),
            'area': int(width * height)
        }
        
        # 在圖片上繪製螺絲孔
        color = self.colors[self.current_hole % len(self.colors)]
        cv2.rectangle(self.display_image, (x_min, y_min), (x_max, y_max), color, 2)
        
        # 標記螺絲孔編號
        cv2.circle(self.display_image, (int(center_x), int(center_y)), 20, color, -1)
        cv2.putText(self.display_image, str(self.current_hole),
                   (int(center_x - 8), int(center_y + 6)),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        print(f'✅ 螺絲孔 {self.current_hole} 已標記: 中心({int(center_x)}, {int(center_y)}) 大小({int(width)}x{int(height)})')
        
        self.current_hole += 1
        
        if self.current_hole > 16:
            print('🎉 所有16個螺絲孔已標記完成！')
    
    def delete_last_hole(self):
        """刪除最後一個螺絲孔"""
        if self.current_hole > 1:
            self.current_hole -= 1
            if self.current_hole in self.positions:
                del self.positions[self.current_hole]
            self.redraw_display()
            print(f'🗑️ 已刪除螺絲孔 {self.current_hole + 1}')
    
    def redraw_display(self):
        """重新繪製顯示圖片"""
        self.display_image = self.image.copy()
        
        for hole_id, hole_info in self.positions.items():
            color = self.colors[hole_id % len(self.colors)]
            
            # 繪製矩形框
            cv2.rectangle(self.display_image, 
                         (hole_info['x_min'], hole_info['y_min']),
                         (hole_info['x_max'], hole_info['y_max']), 
                         color, 2)
            
            # 繪製編號圓圈
            cv2.circle(self.display_image, 
                      (hole_info['center_x'], hole_info['center_y']), 
                      20, color, -1)
            cv2.putText(self.display_image, str(hole_id),
                       (hole_info['center_x'] - 8, hole_info['center_y'] + 6),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    def save_configuration(self):
        """保存配置檔案"""
        if len(self.positions) < 16:
            print(f'⚠️ 請先標記所有16個螺絲孔 (目前: {len(self.positions)}/16)')
            return False
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 準備匯出數據
        export_data = {
            'metadata': {
                'timestamp': timestamp,
                'total_holes': len(self.positions),
                'software': '螺絲孔標記軟體 v1.0',
                'description': '16個螺絲孔位置配置'
            },
            'screw_holes': self.positions
        }
        
        # 保存JSON配置檔
        json_file = f'螺絲孔配置_{timestamp}.json'
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        # 生成Python代碼
        py_file = f'螺絲孔代碼_{timestamp}.py'
        self.generate_python_code(py_file, timestamp)
        
        # 生成CSV檔案
        csv_file = f'螺絲孔清單_{timestamp}.csv'
        self.generate_csv_file(csv_file)
        
        # 保存標記圖片
        img_file = f'螺絲孔標記圖_{timestamp}.jpg'
        cv2.imwrite(img_file, self.display_image)
        
        print(f'\n📁 配置檔案已匯出:')
        print(f'  ✅ {json_file} - JSON配置檔')
        print(f'  ✅ {py_file} - Python代碼')
        print(f'  ✅ {csv_file} - CSV清單')
        print(f'  ✅ {img_file} - 標記圖片')
        
        return True
    
    def generate_python_code(self, filename, timestamp):
        """生成Python代碼"""
        code = f'''# 螺絲孔位置配置 - {timestamp}
# 由螺絲孔標記軟體自動生成

def define_screw_positions(self):
    """定義16個螺絲孔位置 - 手動標記版本"""
    print('📍 載入手動標記的16個螺絲孔位置...')
    
    positions = {{'''
        
        for hole_id in sorted(self.positions.keys()):
            hole = self.positions[hole_id]
            code += f'''
        {hole_id}: {{
            'x_min': {hole['x_min']},
            'x_max': {hole['x_max']},
            'y_min': {hole['y_min']},
            'y_max': {hole['y_max']},
            'center': ({hole['center_x']}, {hole['center_y']}),
            'width': {hole['width']},
            'height': {hole['height']}
        }},'''
        
        code += '''
    }}
    
    self.screw_positions = positions
    print(f'✅ 載入了 {{len(positions)}} 個手動標記的螺絲孔位置')
    
    # 顯示位置資訊
    print('📍 螺絲孔位置清單:')
    for i in range(1, 17):
        if i in positions:
            pos = positions[i]
            print(f'  螺絲孔{i:2d}: 中心({pos["center"][0]}, {pos["center"][1]}) 範圍({pos["x_min"]}-{pos["x_max"]}, {pos["y_min"]}-{pos["y_max"]})')

# 使用方法：
# 將此函數複製到 sop_compliance_system.py 中，替換原有的 define_screw_positions 函數
'''
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(code)
    
    def generate_csv_file(self, filename):
        """生成CSV清單"""
        import csv
        
        with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            
            # 寫入標題
            writer.writerow(['螺絲孔編號', '中心X', '中心Y', 'X最小', 'X最大', 'Y最小', 'Y最大', '寬度', '高度', '面積'])
            
            # 寫入數據
            for hole_id in sorted(self.positions.keys()):
                hole = self.positions[hole_id]
                writer.writerow([
                    hole_id,
                    hole['center_x'],
                    hole['center_y'],
                    hole['x_min'],
                    hole['x_max'],
                    hole['y_min'],
                    hole['y_max'],
                    hole['width'],
                    hole['height'],
                    hole['area']
                ])
    
    def start_marking(self, video_path, frame_number=100):
        """開始標記螺絲孔"""
        print('🔧 螺絲孔標記軟體')
        print('=' * 50)
        
        # 載入影片幀
        if not self.load_video_frame(video_path, frame_number):
            return False
        
        print('\n📋 操作說明:')
        print('🖱️  拖拽滑鼠圈選螺絲孔位置')
        print('🔢  按順序標記1-16號螺絲孔')
        print('⌨️  按鍵操作:')
        print('   d - 刪除最後一個螺絲孔')
        print('   r - 重置所有標記')
        print('   s - 保存配置檔案')
        print('   q - 退出程式')
        print(f'\n🎯 請開始標記第 {self.current_hole} 個螺絲孔...')
        
        # 設置視窗
        cv2.namedWindow('螺絲孔標記軟體', cv2.WINDOW_NORMAL)
        cv2.resizeWindow('螺絲孔標記軟體', 1200, 800)
        cv2.setMouseCallback('螺絲孔標記軟體', self.mouse_callback)
        
        while True:
            # 更新狀態顯示
            temp_image = self.display_image.copy()
            
            # 顯示狀態資訊
            if self.current_hole <= 16:
                status = f'標記螺絲孔: {self.current_hole}/16 | 已完成: {len(self.positions)}'
            else:
                status = f'所有螺絲孔已標記完成！按 s 保存配置'
            
            cv2.putText(temp_image, status, (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 255), 2)
            
            # 顯示操作提示
            cv2.putText(temp_image, 'D:刪除 R:重置 S:保存 Q:退出', (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
            
            cv2.imshow('螺絲孔標記軟體', temp_image)
            
            key = cv2.waitKey(1) & 0xFF
            
            if key == ord('q'):  # 退出
                break
            elif key == ord('d'):  # 刪除最後一個
                self.delete_last_hole()
            elif key == ord('r'):  # 重置
                self.positions.clear()
                self.current_hole = 1
                self.redraw_display()
                print('🔄 已重置所有標記')
            elif key == ord('s'):  # 保存
                if self.save_configuration():
                    print('✅ 配置檔案保存成功！')
                    break
        
        cv2.destroyAllWindows()
        return len(self.positions) >= 16

def main():
    """主程式"""
    print('🔧 螺絲孔位置標記軟體')
    print('=' * 50)
    
    # 選擇影片
    print('📹 影片選擇:')
    print('1. videos/1.mp4 (訓練影片)')
    print('2. videos/2.mp4 (測試影片)')
    print('3. 自定義路徑')
    
    choice = input('\n請選擇影片 (1/2/3，預設2): ').strip()
    
    if choice == '1':
        video_path = 'videos/1.mp4'
    elif choice == '3':
        video_path = input('請輸入影片路徑: ').strip()
        if not video_path:
            print('❌ 未輸入影片路徑')
            return
    else:
        video_path = 'videos/2.mp4'
    
    # 檢查檔案是否存在
    if not os.path.exists(video_path):
        print(f'❌ 找不到影片檔案: {video_path}')
        return
    
    # 選擇幀數
    frame_input = input('請輸入要使用的幀數 (預設: 100): ').strip()
    frame_number = int(frame_input) if frame_input.isdigit() else 100
    
    # 開始標記
    marker = ScrewHoleMarker()
    
    if marker.start_marking(video_path, frame_number):
        print('\n🎉 螺絲孔標記完成！')
        print('📋 生成的檔案可以直接用於SOP系統配置')
    else:
        print('\n❌ 螺絲孔標記未完成')

if __name__ == "__main__":
    main()
