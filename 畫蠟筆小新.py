#!/usr/bin/env python3
"""
用Python畫蠟筆小新
使用matplotlib繪製可愛的蠟筆小新
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np

def draw_crayon_shin_chan():
    """畫蠟筆小新"""
    
    # 創建畫布
    fig, ax = plt.subplots(1, 1, figsize=(10, 12))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 12)
    ax.set_aspect('equal')
    ax.axis('off')  # 隱藏座標軸
    
    # 設置背景色
    fig.patch.set_facecolor('lightblue')
    
    # 1. 畫頭部 (橢圓形)
    head = patches.Ellipse((5, 9), 3.5, 4, facecolor='#FFDBAC', edgecolor='black', linewidth=2)
    ax.add_patch(head)
    
    # 2. 畫眉毛 (粗黑線)
    # 左眉毛
    ax.plot([3.5, 4.2], [10.2, 10.5], 'k-', linewidth=8)
    # 右眉毛  
    ax.plot([5.8, 6.5], [10.5, 10.2], 'k-', linewidth=8)
    
    # 3. 畫眼睛
    # 左眼 (橢圓)
    left_eye = patches.Ellipse((4, 9.8), 0.8, 0.6, facecolor='white', edgecolor='black', linewidth=2)
    ax.add_patch(left_eye)
    # 左眼珠
    left_pupil = patches.Circle((4, 9.8), 0.15, facecolor='black')
    ax.add_patch(left_pupil)
    
    # 右眼 (橢圓)
    right_eye = patches.Ellipse((6, 9.8), 0.8, 0.6, facecolor='white', edgecolor='black', linewidth=2)
    ax.add_patch(right_eye)
    # 右眼珠
    right_pupil = patches.Circle((6, 9.8), 0.15, facecolor='black')
    ax.add_patch(right_pupil)
    
    # 4. 畫鼻子 (小圓點)
    nose = patches.Circle((5, 9.2), 0.1, facecolor='black')
    ax.add_patch(nose)
    
    # 5. 畫嘴巴 (弧線)
    theta = np.linspace(0.2*np.pi, 0.8*np.pi, 50)
    mouth_x = 5 + 0.8 * np.cos(theta)
    mouth_y = 8.5 + 0.3 * np.sin(theta)
    ax.plot(mouth_x, mouth_y, 'k-', linewidth=3)
    
    # 6. 畫頭髮 (幾根豎起的線)
    # 中間的頭髮
    ax.plot([5, 5], [11, 11.8], 'k-', linewidth=4)
    # 左邊的頭髮
    ax.plot([4.2, 4], [10.8, 11.5], 'k-', linewidth=3)
    # 右邊的頭髮
    ax.plot([5.8, 6], [10.8, 11.5], 'k-', linewidth=3)
    
    # 7. 畫身體 (矩形)
    body = patches.Rectangle((4, 4.5), 2, 3, facecolor='red', edgecolor='black', linewidth=2)
    ax.add_patch(body)
    
    # 8. 畫手臂
    # 左手臂
    left_arm = patches.Rectangle((2.5, 6), 1.5, 0.8, facecolor='#FFDBAC', edgecolor='black', linewidth=2)
    ax.add_patch(left_arm)
    # 右手臂
    right_arm = patches.Rectangle((6, 6), 1.5, 0.8, facecolor='#FFDBAC', edgecolor='black', linewidth=2)
    ax.add_patch(right_arm)
    
    # 9. 畫手 (圓形)
    # 左手
    left_hand = patches.Circle((2.5, 6.4), 0.4, facecolor='#FFDBAC', edgecolor='black', linewidth=2)
    ax.add_patch(left_hand)
    # 右手
    right_hand = patches.Circle((7.5, 6.4), 0.4, facecolor='#FFDBAC', edgecolor='black', linewidth=2)
    ax.add_patch(right_hand)
    
    # 10. 畫褲子
    pants = patches.Rectangle((4, 2.5), 2, 2, facecolor='yellow', edgecolor='black', linewidth=2)
    ax.add_patch(pants)
    
    # 11. 畫腿
    # 左腿
    left_leg = patches.Rectangle((4, 1), 0.6, 1.5, facecolor='#FFDBAC', edgecolor='black', linewidth=2)
    ax.add_patch(left_leg)
    # 右腿
    right_leg = patches.Rectangle((5.4, 1), 0.6, 1.5, facecolor='#FFDBAC', edgecolor='black', linewidth=2)
    ax.add_patch(right_leg)
    
    # 12. 畫鞋子
    # 左鞋
    left_shoe = patches.Ellipse((4.3, 0.7), 1, 0.6, facecolor='brown', edgecolor='black', linewidth=2)
    ax.add_patch(left_shoe)
    # 右鞋
    right_shoe = patches.Ellipse((5.7, 0.7), 1, 0.6, facecolor='brown', edgecolor='black', linewidth=2)
    ax.add_patch(right_shoe)
    
    # 13. 添加一些細節
    # 在紅色上衣上畫個小圖案
    shirt_detail = patches.Circle((5, 5.5), 0.2, facecolor='white', edgecolor='black', linewidth=1)
    ax.add_patch(shirt_detail)
    
    # 14. 添加標題
    plt.title('🖍️ 蠟筆小新 Crayon Shin-chan 🖍️', fontsize=20, fontweight='bold', pad=20)
    
    # 15. 添加一些裝飾
    # 畫一些小星星
    star_x = [1, 8.5, 1.5, 8, 2, 7.5]
    star_y = [10, 10.5, 8, 8.5, 6, 6.5]
    for x, y in zip(star_x, star_y):
        ax.plot(x, y, '*', markersize=15, color='gold')
    
    # 顯示圖片
    plt.tight_layout()
    plt.show()
    
    # 保存圖片
    plt.savefig('蠟筆小新.png', dpi=300, bbox_inches='tight', facecolor='lightblue')
    print('🎨 蠟筆小新畫好了！已保存為 蠟筆小新.png')

def draw_simple_shin_chan():
    """畫簡化版蠟筆小新"""
    
    fig, ax = plt.subplots(1, 1, figsize=(8, 10))
    ax.set_xlim(0, 8)
    ax.set_ylim(0, 10)
    ax.set_aspect('equal')
    ax.axis('off')
    
    # 背景
    fig.patch.set_facecolor('lightyellow')
    
    # 頭
    head = patches.Circle((4, 7.5), 1.5, facecolor='#FFDBAC', edgecolor='black', linewidth=3)
    ax.add_patch(head)
    
    # 眉毛
    ax.plot([3, 3.5], [8.2, 8.4], 'k-', linewidth=6)
    ax.plot([4.5, 5], [8.4, 8.2], 'k-', linewidth=6)
    
    # 眼睛
    ax.plot(3.5, 7.8, 'ko', markersize=8)
    ax.plot(4.5, 7.8, 'ko', markersize=8)
    
    # 嘴巴
    theta = np.linspace(0.3*np.pi, 0.7*np.pi, 30)
    mouth_x = 4 + 0.5 * np.cos(theta)
    mouth_y = 7 + 0.2 * np.sin(theta)
    ax.plot(mouth_x, mouth_y, 'k-', linewidth=3)
    
    # 頭髮
    ax.plot([4, 4], [9, 9.5], 'k-', linewidth=4)
    
    # 身體
    body = patches.Rectangle((3.2, 4), 1.6, 2.5, facecolor='red', edgecolor='black', linewidth=2)
    ax.add_patch(body)
    
    # 手
    ax.plot(2.5, 5.5, 'o', markersize=12, color='#FFDBAC', markeredgecolor='black', markeredgewidth=2)
    ax.plot(5.5, 5.5, 'o', markersize=12, color='#FFDBAC', markeredgecolor='black', markeredgewidth=2)
    
    # 褲子
    pants = patches.Rectangle((3.2, 2), 1.6, 2, facecolor='yellow', edgecolor='black', linewidth=2)
    ax.add_patch(pants)
    
    # 腿
    ax.plot([3.5, 3.5], [2, 0.5], 'k-', linewidth=8, color='#FFDBAC')
    ax.plot([4.5, 4.5], [2, 0.5], 'k-', linewidth=8, color='#FFDBAC')
    
    # 鞋
    ax.plot(3.5, 0.3, 'o', markersize=15, color='brown', markeredgecolor='black', markeredgewidth=2)
    ax.plot(4.5, 0.3, 'o', markersize=15, color='brown', markeredgecolor='black', markeredgewidth=2)
    
    plt.title('🖍️ 簡化版蠟筆小新 🖍️', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.show()
    
    plt.savefig('簡化版蠟筆小新.png', dpi=300, bbox_inches='tight', facecolor='lightyellow')
    print('🎨 簡化版蠟筆小新畫好了！')

if __name__ == "__main__":
    print('🎨 開始畫蠟筆小新...')
    print('1. 詳細版')
    print('2. 簡化版')
    
    choice = input('請選擇版本 (1/2): ').strip()
    
    if choice == '2':
        draw_simple_shin_chan()
    else:
        draw_crayon_shin_chan()
    
    print('🎉 完成！')
