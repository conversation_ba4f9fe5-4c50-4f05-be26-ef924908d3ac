#!/usr/bin/env python3
"""
時間軸螺絲孔標記工具
可以在影片的不同時間點標記螺絲孔位置
適用於工件會旋轉的情況
"""

import cv2
import json
import numpy as np
import os
from datetime import datetime

class TimelineMarker:
    def __init__(self):
        self.video_path = None
        self.cap = None
        self.total_frames = 0
        self.fps = 30
        self.current_frame = 0
        
        # 標記數據
        self.timeline_marks = {}  # {frame_number: {hole_id: position_data}}
        self.all_holes = {}       # 最終整合的16個螺絲孔位置
        
        # 當前標記狀態
        self.current_image = None
        self.display_image = None
        self.drawing = False
        self.start_point = None
        self.marking_hole_id = None
        
    def load_video(self, video_path):
        """載入影片"""
        self.video_path = video_path
        self.cap = cv2.VideoCapture(video_path)
        
        if not self.cap.isOpened():
            print('❌ 無法開啟影片')
            return False
        
        self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
        self.fps = self.cap.get(cv2.CAP_PROP_FPS)
        
        print(f'✅ 影片載入成功')
        print(f'📊 總幀數: {self.total_frames}')
        print(f'🎬 幀率: {self.fps:.1f} FPS')
        print(f'⏱️ 總時長: {self.total_frames/self.fps:.1f}秒')
        
        return True
    
    def goto_frame(self, frame_number):
        """跳到指定幀"""
        if frame_number < 0:
            frame_number = 0
        elif frame_number >= self.total_frames:
            frame_number = self.total_frames - 1
        
        self.current_frame = frame_number
        self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
        ret, frame = self.cap.read()
        
        if ret:
            self.current_image = frame.copy()
            self.display_image = frame.copy()
            self.draw_existing_marks()
            return True
        return False
    
    def draw_existing_marks(self):
        """繪製已有的標記"""
        # 繪製當前幀的標記
        if self.current_frame in self.timeline_marks:
            marks = self.timeline_marks[self.current_frame]
            for hole_id, pos_data in marks.items():
                color = self.get_hole_color(hole_id)
                
                # 繪製矩形
                cv2.rectangle(self.display_image,
                             (pos_data['x_min'], pos_data['y_min']),
                             (pos_data['x_max'], pos_data['y_max']),
                             color, 2)
                
                # 繪製編號
                cv2.circle(self.display_image,
                          (pos_data['center_x'], pos_data['center_y']),
                          15, color, -1)
                cv2.putText(self.display_image, str(hole_id),
                           (pos_data['center_x'] - 6, pos_data['center_y'] + 4),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    def get_hole_color(self, hole_id):
        """根據螺絲孔ID獲取顏色"""
        colors = [
            (0, 255, 0),    # 綠色
            (255, 0, 0),    # 藍色  
            (0, 0, 255),    # 紅色
            (255, 255, 0),  # 青色
            (255, 0, 255),  # 洋紅
            (0, 255, 255),  # 黃色
        ]
        return colors[hole_id % len(colors)]
    
    def mouse_callback(self, event, x, y, flags, param):
        """滑鼠事件處理"""
        if self.marking_hole_id is None:
            return
        
        if event == cv2.EVENT_LBUTTONDOWN:
            self.drawing = True
            self.start_point = (x, y)
            
        elif event == cv2.EVENT_MOUSEMOVE and self.drawing:
            temp_image = self.display_image.copy()
            color = self.get_hole_color(self.marking_hole_id)
            cv2.rectangle(temp_image, self.start_point, (x, y), color, 2)
            
            text = f'螺絲孔 {self.marking_hole_id}'
            cv2.putText(temp_image, text,
                       (self.start_point[0], self.start_point[1] - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
            
            cv2.imshow('時間軸螺絲孔標記', temp_image)
            
        elif event == cv2.EVENT_LBUTTONUP and self.drawing:
            self.drawing = False
            if abs(x - self.start_point[0]) > 10 and abs(y - self.start_point[1]) > 10:
                self.add_hole_mark(self.start_point, (x, y))
    
    def add_hole_mark(self, start_point, end_point):
        """添加螺絲孔標記"""
        x1, y1 = start_point
        x2, y2 = end_point
        
        x_min, x_max = min(x1, x2), max(x1, x2)
        y_min, y_max = min(y1, y2), max(y1, y2)
        center_x = (x_min + x_max) // 2
        center_y = (y_min + y_max) // 2
        
        # 儲存到時間軸標記
        if self.current_frame not in self.timeline_marks:
            self.timeline_marks[self.current_frame] = {}
        
        self.timeline_marks[self.current_frame][self.marking_hole_id] = {
            'hole_id': self.marking_hole_id,
            'frame': self.current_frame,
            'time_seconds': self.current_frame / self.fps,
            'x_min': x_min,
            'x_max': x_max,
            'y_min': y_min,
            'y_max': y_max,
            'center_x': center_x,
            'center_y': center_y,
            'width': x_max - x_min,
            'height': y_max - y_min
        }
        
        # 更新最終螺絲孔位置 (支援多時間點)
        if self.marking_hole_id not in self.all_holes:
            self.all_holes[self.marking_hole_id] = []

        # 如果是列表，添加新位置；如果不是，轉換為列表
        if isinstance(self.all_holes[self.marking_hole_id], list):
            self.all_holes[self.marking_hole_id].append(self.timeline_marks[self.current_frame][self.marking_hole_id])
        else:
            # 兼容舊格式，轉換為列表
            old_pos = self.all_holes[self.marking_hole_id]
            self.all_holes[self.marking_hole_id] = [old_pos, self.timeline_marks[self.current_frame][self.marking_hole_id]]
        
        time_str = f"{int(self.current_frame / self.fps // 60):02d}:{int(self.current_frame / self.fps % 60):02d}"
        print(f'✅ 螺絲孔 {self.marking_hole_id} 已標記在 {time_str} (第{self.current_frame}幀)')
        
        self.marking_hole_id = None
        self.draw_existing_marks()
    
    def start_marking(self):
        """開始標記流程"""
        print('\n🎯 時間軸螺絲孔標記工具')
        print('=' * 50)

        print('📋 操作說明:')
        print('⏭️  A/D 鍵 : 前後移動幀')
        print('⏭️  W/X 鍵 : 快速跳躍(10秒)')
        print('⏭️  Q/E 鍵 : 快速跳躍(5秒)')
        print('⏭️  R/T 鍵 : 快速跳躍(1秒)')
        print('⏭️  方向鍵 ←/→ : 前後移動幀')
        print('⏭️  方向鍵 ↑/↓ : 快速跳躍(5秒)')
        print('🔢  數字鍵 1-9, 0 : 選擇要標記的螺絲孔(1-10)')
        print('🔢  字母鍵 Z,C,V,B,N,M : 標記螺絲孔11-16')
        print('🖱️  拖拽滑鼠 : 圈選螺絲孔位置')
        print('⌨️  其他按鍵:')
        print('   DEL/Backspace - 刪除當前幀的標記')
        print('   S - 保存配置檔案')
        print('   ESC - 退出程式')
        print('\n💡 重要提示:')
        print('   1. 視窗開啟後，請點擊視窗確保焦點')
        print('   2. 如果按鍵無反應，請關閉視窗重新開始')
        print('   3. 建議先按 A 或 D 鍵測試是否有反應')

        # 跳到第100幀開始
        self.goto_frame(100)

        # 創建視窗並設置屬性
        window_name = '時間軸螺絲孔標記'
        cv2.namedWindow(window_name, cv2.WINDOW_NORMAL | cv2.WINDOW_KEEPRATIO)
        cv2.resizeWindow(window_name, 1200, 800)
        cv2.setMouseCallback(window_name, self.mouse_callback)

        print(f'\n🖥️ OpenCV視窗 "{window_name}" 已開啟')
        print('🔍 請點擊視窗並按 A 鍵測試...')
        
        # 主循環
        frame_counter = 0
        while True:
            # 更新顯示
            temp_image = self.display_image.copy()

            # 顯示狀態資訊
            time_str = f"{int(self.current_frame / self.fps // 60):02d}:{int(self.current_frame / self.fps % 60):02d}"
            status = f'時間: {time_str} | 幀: {self.current_frame}/{self.total_frames} | 已標記: {len(self.all_holes)}/16'
            cv2.putText(temp_image, status, (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)

            # 顯示當前標記模式
            if self.marking_hole_id:
                mode_text = f'標記模式: 螺絲孔 {self.marking_hole_id} (拖拽滑鼠圈選)'
                cv2.putText(temp_image, mode_text, (10, 60),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            else:
                mode_text = '按數字鍵1-9,0選擇螺絲孔1-10，按Z,C,V,B,N,M選擇螺絲孔11-16'
                cv2.putText(temp_image, mode_text, (10, 60),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

            # 顯示已標記的螺絲孔清單
            marked_holes = sorted(self.all_holes.keys())
            holes_text = f'已標記螺絲孔: {marked_holes}'
            cv2.putText(temp_image, holes_text, (10, 90),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)

            # 顯示幀計數器 (用於調試)
            frame_counter += 1
            debug_text = f'循環: {frame_counter} | 按鍵測試: 按A或D鍵'
            cv2.putText(temp_image, debug_text, (10, 120),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (128, 128, 128), 1)

            cv2.imshow('時間軸螺絲孔標記', temp_image)

            # 使用更長的等待時間，確保按鍵檢測
            key = cv2.waitKey(50) & 0xFF

            # 調試：顯示按鍵碼
            if key != 255:  # 有按鍵時
                print(f'🔍 按鍵檢測: {key} (字符: {chr(key) if 32 <= key <= 126 else "特殊鍵"})')
                print(f'   對應操作: ', end='')

                # 立即顯示對應的操作
                if key == ord('q') or key == 27:
                    print('退出程式')
                elif key == ord('a'):
                    print('向前移動一幀')
                elif key == ord('d'):
                    print('向後移動一幀')
                elif key == ord('w'):
                    print('快速向前跳躍10秒')
                elif key == ord('x'):
                    print('快速向後跳躍10秒')
                elif key >= ord('1') and key <= ord('9'):
                    print(f'選擇螺絲孔 {key - ord("0")}')
                elif key == ord('0'):
                    print('選擇螺絲孔 10')
                elif key in [81, 2, 84, 0, 83, 3, 82, 1, 72, 80]:
                    print('方向鍵操作')
                else:
                    print('未知操作')

            # 處理按鍵
            if key == 27:  # ESC 退出
                print('👋 ESC退出程式')
                break
            # 移動控制鍵
            elif key == ord('a'):  # A鍵 = 向前一幀
                print('⬅️ A鍵：向前移動一幀')
                self.goto_frame(self.current_frame - 1)
            elif key == ord('d'):  # D鍵 = 向後一幀
                print('➡️ D鍵：向後移動一幀')
                self.goto_frame(self.current_frame + 1)
            # 跳秒控制鍵
            elif key == ord('w'):  # W鍵 = 向前10秒
                print('⏪ W鍵：快速向前跳躍10秒')
                self.goto_frame(self.current_frame - int(10 * self.fps))
            elif key == ord('x'):  # X鍵 = 向後10秒
                print('⏩ X鍵：快速向後跳躍10秒')
                self.goto_frame(self.current_frame + int(10 * self.fps))
            elif key == ord('q'):  # Q鍵 = 向前5秒
                print('⏪ Q鍵：快速向前跳躍5秒')
                self.goto_frame(self.current_frame - int(5 * self.fps))
            elif key == ord('e'):  # E鍵 = 向後5秒
                print('⏩ E鍵：快速向後跳躍5秒')
                self.goto_frame(self.current_frame + int(5 * self.fps))
            elif key == ord('r'):  # R鍵 = 向前1秒
                print('⏪ R鍵：快速向前跳躍1秒')
                self.goto_frame(self.current_frame - int(1 * self.fps))
            elif key == ord('t'):  # T鍵 = 向後1秒
                print('⏩ T鍵：快速向後跳躍1秒')
                self.goto_frame(self.current_frame + int(1 * self.fps))
            # 方向鍵支援 (多種編碼方式)
            elif key in [81, 2, 84, 0]:  # 左箭頭 (不同系統的編碼)
                print('⬅️ 方向鍵左：向前移動一幀')
                self.goto_frame(self.current_frame - 1)
            elif key in [83, 3, 82, 1]:  # 右箭頭 (不同系統的編碼)
                print('➡️ 方向鍵右：向後移動一幀')
                self.goto_frame(self.current_frame + 1)
            elif key in [82, 0, 72]:  # 上箭頭 (不同系統的編碼)
                print('⬆️ 方向鍵上：快速向前跳躍5秒')
                self.goto_frame(self.current_frame - int(5 * self.fps))
            elif key in [84, 1, 80]:  # 下箭頭 (不同系統的編碼)
                print('⬇️ 方向鍵下：快速向後跳躍5秒')
                self.goto_frame(self.current_frame + int(5 * self.fps))
            # Page Up/Down 支援
            elif key == 85 or key == ord('u'):  # Page Up 或 u
                print('⏪ Page Up：快速向前跳躍10秒')
                self.goto_frame(self.current_frame - int(10 * self.fps))
            elif key == 86 or key == ord('i'):  # Page Down 或 i
                print('⏩ Page Down：快速向後跳躍10秒')
                self.goto_frame(self.current_frame + int(10 * self.fps))
            elif key >= ord('1') and key <= ord('9'):  # 數字1-9
                self.marking_hole_id = key - ord('0')
                print(f'🎯 選擇標記螺絲孔 {self.marking_hole_id}')
            elif key == ord('0'):  # 數字0 = 螺絲孔10
                self.marking_hole_id = 10
                print(f'🎯 選擇標記螺絲孔 {self.marking_hole_id}')
            elif key == ord('!'):  # Shift+1 = 螺絲孔11
                self.marking_hole_id = 11
                print(f'🎯 選擇標記螺絲孔 {self.marking_hole_id}')
            elif key == ord('@'):  # Shift+2 = 螺絲孔12
                self.marking_hole_id = 12
                print(f'🎯 選擇標記螺絲孔 {self.marking_hole_id}')
            elif key == ord('#'):  # Shift+3 = 螺絲孔13
                self.marking_hole_id = 13
                print(f'🎯 選擇標記螺絲孔 {self.marking_hole_id}')
            elif key == ord('$'):  # Shift+4 = 螺絲孔14
                self.marking_hole_id = 14
                print(f'🎯 選擇標記螺絲孔 {self.marking_hole_id}')
            elif key == ord('%'):  # Shift+5 = 螺絲孔15
                self.marking_hole_id = 15
                print(f'🎯 選擇標記螺絲孔 {self.marking_hole_id}')
            elif key == ord('^'):  # Shift+6 = 螺絲孔16
                self.marking_hole_id = 16
                print(f'🎯 選擇標記螺絲孔 {self.marking_hole_id}')
            elif key == 8 or key == 127:  # Backspace 或 Delete - 刪除當前幀標記
                if self.current_frame in self.timeline_marks:
                    del self.timeline_marks[self.current_frame]
                    # 重新整合all_holes
                    self.rebuild_all_holes()
                    self.draw_existing_marks()
                    print(f'🗑️ 已刪除第{self.current_frame}幀的所有標記')
            elif key == ord('s') or key == ord('S'):  # 保存 (大小寫都可以)
                if len(self.all_holes) >= 16:
                    self.save_configuration()
                    print('✅ 配置檔案保存成功！')
                else:
                    print(f'⚠️ 請先標記所有16個螺絲孔 (目前: {len(self.all_holes)}/16)')
            # 增加更多螺絲孔選擇方式
            elif key == ord('z'):  # Z = 螺絲孔11
                self.marking_hole_id = 11
                print(f'🎯 選擇標記螺絲孔 {self.marking_hole_id}')
            elif key == ord('c'):  # C = 螺絲孔12
                self.marking_hole_id = 12
                print(f'🎯 選擇標記螺絲孔 {self.marking_hole_id}')
            elif key == ord('v'):  # V = 螺絲孔13
                self.marking_hole_id = 13
                print(f'🎯 選擇標記螺絲孔 {self.marking_hole_id}')
            elif key == ord('b'):  # B = 螺絲孔14
                self.marking_hole_id = 14
                print(f'🎯 選擇標記螺絲孔 {self.marking_hole_id}')
            elif key == ord('n'):  # N = 螺絲孔15
                self.marking_hole_id = 15
                print(f'🎯 選擇標記螺絲孔 {self.marking_hole_id}')
            elif key == ord('m'):  # M = 螺絲孔16
                self.marking_hole_id = 16
                print(f'🎯 選擇標記螺絲孔 {self.marking_hole_id}')
        
        cv2.destroyAllWindows()
        return len(self.all_holes) >= 16
    
    def rebuild_all_holes(self):
        """重新整合所有螺絲孔位置"""
        self.all_holes.clear()
        for frame_marks in self.timeline_marks.values():
            for hole_id, pos_data in frame_marks.items():
                self.all_holes[hole_id] = pos_data
    
    def save_configuration(self):
        """保存配置檔案"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 準備匯出數據
        export_data = {
            'metadata': {
                'timestamp': timestamp,
                'video_path': self.video_path,
                'total_holes': len(self.all_holes),
                'software': '時間軸螺絲孔標記工具 v1.0',
                'description': '16個螺絲孔在不同時間點的位置'
            },
            'timeline_marks': self.timeline_marks,
            'final_positions': self.all_holes
        }
        
        # 保存JSON配置檔
        json_file = f'時間軸螺絲孔配置_{timestamp}.json'
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        # 生成Python代碼
        py_file = f'時間軸螺絲孔代碼_{timestamp}.py'
        self.generate_python_code(py_file, timestamp)
        
        print(f'\n📁 時間軸配置檔案已匯出:')
        print(f'  ✅ {json_file} - 完整時間軸數據')
        print(f'  ✅ {py_file} - Python代碼')
        
        return True
    
    def generate_python_code(self, filename, timestamp):
        """生成Python代碼"""
        code = f'''# 時間軸螺絲孔位置配置 - {timestamp}
# 由時間軸螺絲孔標記工具自動生成

def define_screw_positions(self):
    """定義16個螺絲孔位置 - 時間軸標記版本"""
    print('📍 載入時間軸標記的16個螺絲孔位置...')
    
    positions = {{'''
        
        for hole_id in sorted(self.all_holes.keys()):
            hole = self.all_holes[hole_id]
            time_str = f"{int(hole['time_seconds'] // 60):02d}:{int(hole['time_seconds'] % 60):02d}"
            code += f'''
        {hole_id}: {{
            'x_min': {hole['x_min']},
            'x_max': {hole['x_max']},
            'y_min': {hole['y_min']},
            'y_max': {hole['y_max']},
            'center': ({hole['center_x']}, {hole['center_y']}),
            'marked_at_time': '{time_str}',
            'marked_at_frame': {hole['frame']}
        }},'''
        
        code += '''
    }}
    
    self.screw_positions = positions
    print(f'✅ 載入了 {{len(positions)}} 個時間軸標記的螺絲孔位置')
    
    # 顯示標記時間資訊
    print('📍 螺絲孔標記時間:')
    for i in range(1, 17):
        if i in positions:
            pos = positions[i]
            print(f'  螺絲孔{i:2d}: {pos["marked_at_time"]} 中心({pos["center"][0]}, {pos["center"][1]})')
'''
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(code)

def main():
    """主程式"""
    print('🎬 時間軸螺絲孔標記工具')
    print('=' * 50)
    
    # 選擇影片
    video_path = input('請輸入影片路徑 (預設: videos/2.mp4): ').strip()
    if not video_path:
        video_path = 'videos/2.mp4'
    
    if not os.path.exists(video_path):
        print(f'❌ 找不到影片檔案: {video_path}')
        return
    
    # 開始標記
    marker = TimelineMarker()
    
    if marker.load_video(video_path):
        if marker.start_marking():
            print('\n🎉 時間軸螺絲孔標記完成！')
        else:
            print('\n❌ 標記未完成')

if __name__ == "__main__":
    main()
