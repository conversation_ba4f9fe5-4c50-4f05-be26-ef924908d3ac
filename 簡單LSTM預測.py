#!/usr/bin/env python3
"""
簡單LSTM預測工具
使用影片1訓練的模型來預測影片2的動作
"""

import pandas as pd
import numpy as np
import pickle
import json
from tensorflow.keras.models import load_model
from datetime import datetime

class SimpleLSTMPredictor:
    def __init__(self):
        self.model = None
        self.scaler = None
        self.label_encoder = None
        self.sequence_length = 30
        
    def load_model(self):
        """載入訓練好的LSTM模型"""
        print('🔍 載入3類LSTM模型...')
        
        try:
            # 載入模型
            self.model = load_model('models/sop_3class_action_model.h5')
            
            # 載入標準化器
            with open('models/sop_3class_scaler.pkl', 'rb') as f:
                self.scaler = pickle.load(f)
            
            # 載入標籤編碼器
            with open('models/sop_3class_label_encoder.pkl', 'rb') as f:
                self.label_encoder = pickle.load(f)
            
            print('✅ 模型載入成功')
            print(f'✅ 支援動作: {list(self.label_encoder.classes_)}')
            return True
            
        except Exception as e:
            print(f'❌ 模型載入失敗: {e}')
            return False
    
    def predict_actions(self, csv_file):
        """預測CSV檔案中的動作"""
        print(f'🎯 開始預測: {csv_file}')
        
        # 載入CSV數據
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        print(f'✅ 載入數據: {len(df)} 幀')
        
        # 提取特徵 (與訓練時相同的8個關鍵點)
        key_landmarks = ['左肩膀', '右肩膀', '左手肘', '右手肘', '左手腕', '右手腕', '左手食指', '右手食指']
        
        feature_columns = []
        for name in key_landmarks:
            x_col = f'{name}_X座標'
            y_col = f'{name}_Y座標'
            z_col = f'{name}_Z座標'
            
            if x_col in df.columns and y_col in df.columns and z_col in df.columns:
                feature_columns.extend([x_col, y_col, z_col])
        
        print(f'📋 使用特徵: {len(feature_columns)} 個')
        
        # 提取特徵
        features = df[feature_columns].values
        
        # 特徵標準化
        scaled_features = self.scaler.transform(features)
        
        # 準備結果
        results = []
        
        print('🔍 開始逐幀預測...')
        
        # 逐幀預測
        for i in range(len(scaled_features) - self.sequence_length + 1):
            # 創建序列
            sequence = scaled_features[i:i + self.sequence_length]
            sequence = sequence.reshape(1, self.sequence_length, -1)
            
            # 預測
            prediction = self.model.predict(sequence, verbose=0)
            predicted_class = np.argmax(prediction[0])
            confidence = float(np.max(prediction[0]))
            action = self.label_encoder.inverse_transform([predicted_class])[0]
            
            # 獲取當前幀信息
            frame_idx = i + self.sequence_length - 1
            frame_data = df.iloc[frame_idx]
            
            result = {
                'frame': frame_idx,
                'time': frame_data.get('影片時間', f'{frame_idx/30:.2f}s'),
                'action': action,
                'confidence': confidence
            }
            
            results.append(result)
            
            # 顯示進度
            if (i + 1) % 200 == 0:
                progress = (i + 1) / (len(scaled_features) - self.sequence_length + 1) * 100
                print(f'  進度: {progress:.1f}%')
        
        return pd.DataFrame(results)
    
    def save_results(self, results_df, output_file=None):
        """保存預測結果"""
        if output_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f'LSTM預測結果_{timestamp}.csv'
        
        results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f'📁 結果已保存: {output_file}')
        
        # 顯示統計
        action_counts = results_df['action'].value_counts()
        print('\n📊 動作統計:')
        for action, count in action_counts.items():
            percentage = count / len(results_df) * 100
            print(f'  {action}: {count} 幀 ({percentage:.1f}%)')
        
        return output_file
    
    def analyze_results(self, results_df):
        """分析預測結果"""
        print('\n📈 預測結果分析:')
        print('=' * 50)
        
        total_frames = len(results_df)
        print(f'總幀數: {total_frames}')
        
        # 動作分布
        action_counts = results_df['action'].value_counts()
        print('\n動作分布:')
        for action, count in action_counts.items():
            percentage = count / total_frames * 100
            print(f'  {action}: {count} 幀 ({percentage:.1f}%)')
        
        # 平均信心度
        avg_confidence = results_df['confidence'].mean()
        print(f'\n平均信心度: {avg_confidence:.3f}')
        
        # 高信心度預測
        high_confidence = results_df[results_df['confidence'] > 0.8]
        print(f'高信心度預測 (>0.8): {len(high_confidence)} 幀 ({len(high_confidence)/total_frames*100:.1f}%)')

def main():
    """主程式"""
    print('🎬 簡單LSTM預測工具')
    print('=' * 50)
    
    # 創建預測器
    predictor = SimpleLSTMPredictor()
    
    # 載入模型
    if not predictor.load_model():
        return
    
    # 預測影片2的動作
    csv_file = '2-pose/2_pose_data_繁體中文_清理版_含時間.csv'
    
    try:
        results_df = predictor.predict_actions(csv_file)
        
        # 保存結果
        output_file = predictor.save_results(results_df)
        
        # 分析結果
        predictor.analyze_results(results_df)
        
        print('\n✅ LSTM預測完成！')
        print(f'📁 詳細結果: {output_file}')
        
    except Exception as e:
        print(f'❌ 預測失敗: {e}')

if __name__ == "__main__":
    main()
