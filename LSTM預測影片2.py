#!/usr/bin/env python3
"""
使用影片1訓練的LSTM模型預測影片2的動作
"""

import pandas as pd
import numpy as np
import pickle
from tensorflow.keras.models import load_model
from datetime import datetime

def main():
    print('🎬 LSTM預測影片2動作')
    print('=' * 50)
    
    # 載入模型
    print('🔍 載入3類LSTM模型...')
    model = load_model('models/sop_3class_action_model.h5')
    
    with open('models/sop_3class_label_encoder.pkl', 'rb') as f:
        label_encoder = pickle.load(f)
    
    with open('models/sop_3class_scaler.pkl', 'rb') as f:
        scaler = pickle.load(f)
    
    print('✅ 模型載入成功')
    print(f'✅ 支援動作: {list(label_encoder.classes_)}')
    
    # 載入影片2數據
    print('📊 載入影片2數據...')
    df = pd.read_csv('2-pose/2_pose_data_繁體中文_清理版_含時間.csv', encoding='utf-8-sig')
    print(f'✅ 載入 {len(df)} 幀數據')
    
    # 提取特徵 (與訓練時相同的8個關鍵點)
    key_landmarks = ['左肩膀', '右肩膀', '左手肘', '右手肘', '左手腕', '右手腕', '左手食指', '右手食指']
    
    feature_columns = []
    for name in key_landmarks:
        x_col = f'{name}_X座標'
        y_col = f'{name}_Y座標'
        z_col = f'{name}_Z座標'
        
        if x_col in df.columns and y_col in df.columns and z_col in df.columns:
            feature_columns.extend([x_col, y_col, z_col])
    
    print(f'📋 使用特徵: {len(feature_columns)} 個')
    
    # 提取特徵
    features = df[feature_columns].values
    
    # 特徵標準化
    scaled_features = scaler.transform(features)
    
    # 創建序列 (30幀序列)
    sequence_length = 30
    sequences = []
    
    for i in range(len(scaled_features) - sequence_length + 1):
        sequence = scaled_features[i:i + sequence_length]
        sequences.append(sequence)
    
    sequences = np.array(sequences)
    print(f'🔄 生成 {len(sequences)} 個測試序列')
    
    # 模型預測
    print('🤖 模型預測中...')
    predictions = model.predict(sequences, verbose=1)
    predicted_classes = np.argmax(predictions, axis=1)
    predicted_labels = label_encoder.inverse_transform(predicted_classes)
    
    # 準備結果
    results = []
    for i, pred_label in enumerate(predicted_labels):
        frame_idx = i + sequence_length - 1
        frame_data = df.iloc[frame_idx]
        
        result = {
            'frame': frame_idx,
            'time': frame_data.get('影片時間', f'{frame_idx/30:.2f}s'),
            'predicted_action': pred_label,
            'confidence': float(np.max(predictions[i]))
        }
        results.append(result)
    
    # 轉換為DataFrame
    results_df = pd.DataFrame(results)
    
    # 保存結果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f'影片2_LSTM預測結果_{timestamp}.csv'
    results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f'\n📁 結果已保存: {output_file}')
    
    # 顯示統計
    action_counts = results_df['predicted_action'].value_counts()
    print('\n📊 動作預測統計:')
    for action, count in action_counts.items():
        percentage = count / len(results_df) * 100
        print(f'  {action}: {count} 幀 ({percentage:.1f}%)')
    
    # 平均信心度
    avg_confidence = results_df['confidence'].mean()
    print(f'\n📈 平均預測信心度: {avg_confidence:.3f}')
    
    # 高信心度預測
    high_confidence = results_df[results_df['confidence'] > 0.8]
    print(f'📈 高信心度預測 (>0.8): {len(high_confidence)} 幀 ({len(high_confidence)/len(results_df)*100:.1f}%)')
    
    print('\n✅ LSTM預測完成！')

if __name__ == "__main__":
    main()
