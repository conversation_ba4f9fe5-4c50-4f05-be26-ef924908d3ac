#!/usr/bin/env python3
"""
螺絲位置手動定義工具
讓用戶在影片截圖上手動圈選16個螺絲位置
"""

import cv2
import json
import numpy as np
from datetime import datetime

class ScrewPositionDefiner:
    def __init__(self):
        self.positions = {}
        self.current_position = 1
        self.image = None
        self.temp_image = None
        self.drawing = False
        self.start_point = None
        
    def load_video_frame(self, video_path, frame_number=100):
        """載入影片的某一幀作為背景"""
        cap = cv2.VideoCapture(video_path)
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
        ret, frame = cap.read()
        cap.release()
        
        if ret:
            self.image = frame.copy()
            self.temp_image = frame.copy()
            return True
        return False
    
    def mouse_callback(self, event, x, y, flags, param):
        """滑鼠回調函數"""
        if event == cv2.EVENT_LBUTTONDOWN:
            self.drawing = True
            self.start_point = (x, y)
            
        elif event == cv2.EVENT_MOUSEMOVE:
            if self.drawing:
                self.temp_image = self.image.copy()
                # 繪製矩形
                cv2.rectangle(self.temp_image, self.start_point, (x, y), (0, 255, 0), 2)
                # 顯示位置編號
                cv2.putText(self.temp_image, f'Position {self.current_position}', 
                           (self.start_point[0], self.start_point[1]-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                
        elif event == cv2.EVENT_LBUTTONUP:
            if self.drawing:
                self.drawing = False
                # 確認選擇區域
                if abs(x - self.start_point[0]) > 10 and abs(y - self.start_point[1]) > 10:
                    self.add_position(self.start_point, (x, y))
    
    def add_position(self, start_point, end_point):
        """添加螺絲位置"""
        x1, y1 = start_point
        x2, y2 = end_point
        
        # 確保座標順序正確
        x_min, x_max = min(x1, x2), max(x1, x2)
        y_min, y_max = min(y1, y2), max(y1, y2)
        
        # 計算中心點
        center_x = (x_min + x_max) / 2
        center_y = (y_min + y_max) / 2
        
        # 儲存位置資訊
        self.positions[self.current_position] = {
            'x_min': x_min,
            'x_max': x_max,
            'y_min': y_min,
            'y_max': y_max,
            'center_x': center_x,
            'center_y': center_y,
            'width': x_max - x_min,
            'height': y_max - y_min
        }
        
        # 在圖片上永久繪製
        cv2.rectangle(self.image, (x_min, y_min), (x_max, y_max), (0, 255, 0), 2)
        cv2.putText(self.image, str(self.current_position), 
                   (int(center_x-10), int(center_y+5)),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
        
        print(f'✅ 位置{self.current_position}已定義: ({x_min},{y_min}) 到 ({x_max},{y_max})')
        
        self.current_position += 1
        self.temp_image = self.image.copy()
    
    def define_positions(self, video_path):
        """開始定義螺絲位置"""
        print('🎯 螺絲位置定義工具')
        print('=' * 50)
        
        # 載入影片幀
        if not self.load_video_frame(video_path):
            print('❌ 無法載入影片')
            return False
        
        print('📋 操作說明:')
        print('1. 用滑鼠拖拽圈選螺絲位置')
        print('2. 按順序選擇1-16號螺絲位置')
        print('3. 按 "r" 重置當前選擇')
        print('4. 按 "u" 撤銷上一個位置')
        print('5. 按 "s" 保存位置設定')
        print('6. 按 "q" 退出')
        print()
        print(f'🔧 請圈選第 {self.current_position} 個螺絲位置...')
        
        # 設置視窗
        cv2.namedWindow('螺絲位置定義', cv2.WINDOW_NORMAL)
        cv2.resizeWindow('螺絲位置定義', 1200, 800)
        cv2.setMouseCallback('螺絲位置定義', self.mouse_callback)
        
        while True:
            cv2.imshow('螺絲位置定義', self.temp_image)
            key = cv2.waitKey(1) & 0xFF
            
            if key == ord('q'):  # 退出
                break
            elif key == ord('r'):  # 重置
                self.temp_image = self.image.copy()
                print('🔄 重置當前選擇')
            elif key == ord('u'):  # 撤銷
                if self.current_position > 1:
                    self.undo_last_position()
            elif key == ord('s'):  # 保存
                if len(self.positions) >= 16:
                    self.save_positions()
                    print('✅ 位置設定已保存')
                else:
                    print(f'⚠️ 請先定義所有16個位置 (目前: {len(self.positions)}/16)')
            
            # 更新提示
            if self.current_position <= 16:
                status = f'請圈選第 {self.current_position} 個螺絲位置 | 已完成: {len(self.positions)}/16'
            else:
                status = f'所有位置已定義完成！按 "s" 保存設定'
            
            # 在圖片上顯示狀態
            temp_display = self.temp_image.copy()
            cv2.putText(temp_display, status, (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.imshow('螺絲位置定義', temp_display)
        
        cv2.destroyAllWindows()
        return len(self.positions) >= 16
    
    def undo_last_position(self):
        """撤銷上一個位置"""
        if self.current_position > 1:
            self.current_position -= 1
            if self.current_position in self.positions:
                del self.positions[self.current_position]
            
            # 重新繪製圖片
            self.redraw_image()
            print(f'↩️ 撤銷位置{self.current_position + 1}')
    
    def redraw_image(self):
        """重新繪製圖片"""
        # 重新載入原始圖片
        self.image = cv2.imread('temp_frame.jpg') if hasattr(self, 'temp_frame_saved') else self.image
        
        # 重新繪製所有已定義的位置
        for pos_id, pos_info in self.positions.items():
            x_min, y_min = int(pos_info['x_min']), int(pos_info['y_min'])
            x_max, y_max = int(pos_info['x_max']), int(pos_info['y_max'])
            center_x, center_y = int(pos_info['center_x']), int(pos_info['center_y'])
            
            cv2.rectangle(self.image, (x_min, y_min), (x_max, y_max), (0, 255, 0), 2)
            cv2.putText(self.image, str(pos_id), (center_x-10, center_y+5),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
        
        self.temp_image = self.image.copy()
    
    def save_positions(self):
        """保存位置設定"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存為JSON格式
        config = {
            'timestamp': timestamp,
            'total_positions': len(self.positions),
            'positions': self.positions
        }
        
        with open(f'螺絲位置設定_{timestamp}.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        # 生成Python代碼
        self.generate_python_code(timestamp)
        
        print(f'📁 位置設定已保存:')
        print(f'  - 螺絲位置設定_{timestamp}.json')
        print(f'  - 螺絲位置代碼_{timestamp}.py')
    
    def generate_python_code(self, timestamp):
        """生成可直接使用的Python代碼"""
        code = f'''# 螺絲位置設定 - {timestamp}
# 可直接複製到 sop_compliance_system.py 中使用

def define_screw_positions(self):
    """定義16個螺絲位置座標範圍 - 手動定義版本"""
    print('📍 載入手動定義的16個螺絲位置...')
    
    positions = {{'''
        
        for pos_id, pos_info in self.positions.items():
            code += f'''
        {pos_id}: {{
            'x_min': {pos_info['x_min']:.0f},
            'x_max': {pos_info['x_max']:.0f},
            'y_min': {pos_info['y_min']:.0f},
            'y_max': {pos_info['y_max']:.0f},
            'center': ({pos_info['center_x']:.0f}, {pos_info['center_y']:.0f})
        }},'''
        
        code += '''
    }
    
    self.screw_positions = positions
    print(f'✅ 載入了 {len(positions)} 個手動定義的螺絲位置')
    
    # 顯示位置定義
    print('📍 螺絲位置定義:')
    for i in range(1, 17):
        if i in positions:
            pos = positions[i]
            print(f'  位置{i:2d}: X({pos["x_min"]:.0f}-{pos["x_max"]:.0f}) Y({pos["y_min"]:.0f}-{pos["y_max"]:.0f})')
'''
        
        with open(f'螺絲位置代碼_{timestamp}.py', 'w', encoding='utf-8') as f:
            f.write(code)

def main():
    """主函數"""
    print('🔧 螺絲位置手動定義工具')
    print('=' * 50)
    
    # 選擇影片
    video_path = input('請輸入影片路徑 (預設: videos/2.mp4): ').strip()
    if not video_path:
        video_path = 'videos/2.mp4'
    
    # 選擇幀數
    frame_input = input('請輸入要使用的幀數 (預設: 100): ').strip()
    frame_number = int(frame_input) if frame_input.isdigit() else 100
    
    # 開始定義
    definer = ScrewPositionDefiner()
    
    if definer.define_positions(video_path):
        print('🎉 螺絲位置定義完成！')
        print('💡 您可以將生成的代碼複製到 sop_compliance_system.py 中使用')
    else:
        print('❌ 位置定義未完成')

if __name__ == "__main__":
    main()
