import numpy as np
import cv2
import mediapipe as mp
import pandas as pd
from collections import deque
import math
import os

# 可選的深度學習依賴
try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential
    from tensorflow.keras.layers import LSTM, Dense, Dropout
    TF_AVAILABLE = True
except ImportError:
    print("⚠️ TensorFlow未安裝，將跳過LSTM模型功能")
    TF_AVAILABLE = False

try:
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    print("⚠️ scikit-learn未安裝，將使用替代相似度計算")
    SKLEARN_AVAILABLE = False

class ScrewingMonitor:
    def __init__(self):
        self.mp_pose = mp.solutions.pose
        self.pose = self.mp_pose.Pose(
            static_image_mode=False,
            model_complexity=1,
            enable_segmentation=False,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )
        
        # 關鍵姿態點索引（只需要8個點）
        self.key_points = {
            'right_shoulder': 12,
            'right_elbow': 14,
            'right_wrist': 16,
            'right_index': 20,
            'left_shoulder': 11,
            'left_elbow': 13,
            'left_wrist': 15,
            'left_index': 19
        }
        
        # 狀態機
        self.state = 'idle'  # idle -> approaching -> engaging -> rotating -> complete
        self.rotation_count = 0
        self.current_screw = None
        
        # 特徵緩衝區
        self.feature_buffer = deque(maxlen=30)  # 30幀序列
        self.wrist_history = deque(maxlen=10)   # 用於旋轉檢測
        
        # 螺絲位置模板（需要根據實際工件調整）
        self.screw_templates = self.create_screw_templates()
        
    def create_screw_templates(self):
        """創建16個螺絲位置的特徵模板"""
        templates = {}
        # 這裡需要根據實際工件的螺絲布局來定義
        # 示例：假設螺絲分佈在4x4網格中
        for i in range(1, 17):
            row = (i - 1) // 4
            col = (i - 1) % 4
            # 正規化的位置特徵
            templates[i] = {
                'position': np.array([col/3, row/3]),  # 0-1範圍
                'approach_angle': math.atan2(row-1.5, col-1.5)  # 接近角度
            }
        return templates
    
    def extract_pose_features(self, landmarks):
        """提取關鍵姿態特徵"""
        if not landmarks:
            return None
            
        # 獲取關鍵點座標
        key_coords = {}
        for name, idx in self.key_points.items():
            if idx < len(landmarks.landmark):
                lm = landmarks.landmark[idx]
                key_coords[name] = np.array([lm.x, lm.y, lm.z])
        
        # 計算特徵
        features = []
        
        # 1. 右手臂姿態特徵
        if all(k in key_coords for k in ['right_shoulder', 'right_elbow', 'right_wrist']):
            # 前臂角度
            forearm_vector = key_coords['right_wrist'] - key_coords['right_elbow']
            upper_arm_vector = key_coords['right_elbow'] - key_coords['right_shoulder']
            
            forearm_angle = self.calculate_angle(forearm_vector, upper_arm_vector)
            features.append(forearm_angle)
            
            # 手腕位置（正規化）
            wrist_pos = key_coords['right_wrist'][:2]  # 只取x,y
            features.extend(wrist_pos)
            
            # 手臂伸展度
            arm_extension = np.linalg.norm(key_coords['right_wrist'] - key_coords['right_shoulder'])
            features.append(arm_extension)
        
        # 2. 手部穩定度特徵
        if 'right_wrist' in key_coords:
            self.wrist_history.append(key_coords['right_wrist'])
            if len(self.wrist_history) > 1:
                # 手腕穩定度（變化量）
                wrist_stability = np.std([np.linalg.norm(pos) for pos in self.wrist_history])
                features.append(wrist_stability)
            else:
                features.append(0.0)
        
        # 3. 工具方向估計
        if all(k in key_coords for k in ['right_elbow', 'right_wrist', 'right_index']):
            tool_direction = self.estimate_tool_direction(
                key_coords['right_elbow'], 
                key_coords['right_wrist'], 
                key_coords['right_index']
            )
            features.extend(tool_direction)
        
        return np.array(features) if features else None
    
    def calculate_angle(self, v1, v2):
        """計算兩個向量的夾角"""
        cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
        cos_angle = np.clip(cos_angle, -1.0, 1.0)
        return np.arccos(cos_angle)
    
    def estimate_tool_direction(self, elbow, wrist, index):
        """估計工具方向"""
        # 前臂方向
        forearm_dir = wrist - elbow
        # 手指方向
        finger_dir = index - wrist
        # 工具軸向近似為前臂延伸方向
        tool_dir = forearm_dir / np.linalg.norm(forearm_dir)
        return tool_dir[:2]  # 只返回x,y分量
    
    def classify_screw_position(self, features):
        """分類螺絲位置（1-16）"""
        if features is None or len(features) < 2:
            return None
            
        wrist_pos = features[1:3]  # 手腕位置
        
        best_match = None
        best_similarity = -1
        
        for screw_id, template in self.screw_templates.items():
            # 計算位置相似度
            similarity = 1 - np.linalg.norm(wrist_pos - template['position'])
            
            if similarity > best_similarity:
                best_similarity = similarity
                best_match = screw_id
        
        return best_match if best_similarity > 0.7 else None
    
    def detect_rotation(self, features_sequence):
        """檢測旋轉動作"""
        if len(features_sequence) < 5:
            return 0
            
        # 分析手腕位置的週期性變化
        wrist_positions = [f[1:3] for f in features_sequence if f is not None]
        
        if len(wrist_positions) < 5:
            return 0
        
        # 計算位置變化的週期性
        distances = []
        for i in range(1, len(wrist_positions)):
            dist = np.linalg.norm(wrist_positions[i] - wrist_positions[i-1])
            distances.append(dist)
        
        # 簡單的峰值檢測來計算旋轉次數
        rotations = self.count_rotations_from_motion(distances)
        return rotations
    
    def count_rotations_from_motion(self, motion_sequence, threshold=0.01):
        """從運動序列中計算旋轉次數"""
        if len(motion_sequence) < 3:
            return 0
            
        # 尋找運動峰值
        peaks = 0
        for i in range(1, len(motion_sequence) - 1):
            if (motion_sequence[i] > motion_sequence[i-1] and 
                motion_sequence[i] > motion_sequence[i+1] and 
                motion_sequence[i] > threshold):
                peaks += 1
        
        # 假設每個峰值代表約1/3圈旋轉
        return peaks // 3
    
    def update_state_machine(self, features, screw_position):
        """更新狀態機"""
        if features is None:
            return
            
        wrist_stability = features[4] if len(features) > 4 else 0
        
        if self.state == 'idle':
            if screw_position is not None:
                self.state = 'approaching'
                self.current_screw = screw_position
                print(f"開始接近螺絲 #{screw_position}")
        
        elif self.state == 'approaching':
            if wrist_stability < 0.005:  # 手腕穩定
                self.state = 'engaging'
                print(f"開始接觸螺絲 #{self.current_screw}")
        
        elif self.state == 'engaging':
            if wrist_stability > 0.01:  # 開始有運動
                self.state = 'rotating'
                self.rotation_count = 0
                print(f"開始旋轉螺絲 #{self.current_screw}")
        
        elif self.state == 'rotating':
            # 檢測旋轉完成
            if len(self.feature_buffer) >= 10:
                recent_features = list(self.feature_buffer)[-10:]
                current_rotations = self.detect_rotation(recent_features)
                
                if current_rotations >= 3:  # 完成3次旋轉
                    self.state = 'complete'
                    print(f"螺絲 #{self.current_screw} 完成！旋轉次數：{current_rotations}")
                    return True  # 返回完成信號
        
        elif self.state == 'complete':
            # 重置狀態
            self.state = 'idle'
            self.current_screw = None
            self.rotation_count = 0
        
        return False
    
    def process_frame(self, frame):
        """處理單幀圖像"""
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results = self.pose.process(rgb_frame)
        
        if results.pose_landmarks:
            # 提取特徵
            features = self.extract_pose_features(results.pose_landmarks)
            
            if features is not None:
                self.feature_buffer.append(features)
                
                # 分類螺絲位置
                screw_position = self.classify_screw_position(features)
                
                # 更新狀態機
                is_complete = self.update_state_machine(features, screw_position)
                
                return {
                    'screw_position': screw_position,
                    'state': self.state,
                    'rotation_count': self.rotation_count,
                    'is_complete': is_complete,
                    'features': features
                }
        
        return None

# LSTM模型構建
def build_lstm_model():
    """構建LSTM模型用於序列分析"""

    if not TF_AVAILABLE:
        print("⚠️ TensorFlow不可用，返回None模型")
        return None, None

    try:
        # 螺絲位置分類模型
        position_model = Sequential([
            LSTM(64, return_sequences=True, input_shape=(30, 8)),  # 30幀，8個特徵
            Dropout(0.3),
            LSTM(32, return_sequences=False),
            Dense(32, activation='relu'),
            Dense(16, activation='softmax')  # 16個螺絲位置
        ])

        # 旋轉計數模型
        rotation_model = Sequential([
            LSTM(32, input_shape=(30, 8)),
            Dense(16, activation='relu'),
            Dense(1, activation='linear')  # 輸出旋轉次數
        ])

        position_model.compile(
            optimizer='adam',
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )

        rotation_model.compile(
            optimizer='adam',
            loss='mse',
            metrics=['mae']
        )

        return position_model, rotation_model

    except Exception as e:
        print(f"❌ LSTM模型構建失敗: {e}")
        return None, None

# 建議的檔案結構：
# main.py - 主程式
# screw_monitor.py - 監控核心類別
# lstm_models.py - LSTM模型定義
# config.py - 配置參數
# utils.py - 工具函數
# data_collector.py - 數據收集工具

# CSV測試功能
def test_with_csv_data(csv_file):
    """使用CSV數據測試螺絲監控系統"""
    print("🔧 開始CSV數據測試...")

    # 載入CSV數據
    df = pd.read_csv(csv_file, encoding='utf-8-sig')
    print(f"✅ 載入 {len(df)} 行數據")

    # 創建監控器
    monitor = ScrewingMonitor()

    # 測試結果記錄
    test_results = []
    completed_screws = set()
    screw_detection_log = []

    # 模擬處理每一幀
    for idx, row in df.iterrows():
        # 從CSV提取關鍵點座標
        landmarks = extract_landmarks_from_csv_row(row)

        if landmarks is not None:
            # 提取特徵
            features = monitor.extract_pose_features(landmarks)

            if features is not None:
                monitor.feature_buffer.append(features)

                # 分類螺絲位置
                screw_position = monitor.classify_screw_position(features)

                # 更新狀態機
                is_complete = monitor.update_state_machine(features, screw_position)

                # 記錄結果
                result = {
                    'frame': idx,
                    'time': row.get('影片時間', ''),
                    'label': row.get('標籤', ''),
                    'detected_screw': screw_position,
                    'state': monitor.state,
                    'rotation_count': monitor.rotation_count,
                    'is_complete': is_complete,
                    'force_detected': row.get('ML預測_出力', False),
                    'force_probability': row.get('出力機率', 0.0)
                }

                test_results.append(result)

                # 記錄螺絲檢測
                if screw_position is not None:
                    screw_detection_log.append({
                        'frame': idx,
                        'time': row.get('影片時間', ''),
                        'actual_label': row.get('標籤', ''),
                        'detected_screw': screw_position,
                        'state': monitor.state
                    })

                # 記錄完成的螺絲
                if is_complete and screw_position:
                    completed_screws.add(screw_position)
                    print(f"🎯 檢測到螺絲 #{screw_position} 完成 (幀 {idx}, 時間 {row.get('影片時間', '')})")

    # 生成測試報告
    generate_test_report(test_results, screw_detection_log, completed_screws, df)

    return test_results

def extract_landmarks_from_csv_row(row):
    """從CSV行中提取關鍵點座標"""
    try:
        # 創建模擬的landmarks對象
        class MockLandmark:
            def __init__(self, x, y, z):
                self.x = x
                self.y = y
                self.z = z

        class MockLandmarks:
            def __init__(self):
                self.landmark = []

        landmarks = MockLandmarks()

        # MediaPipe 33個關鍵點
        landmark_names = [
            '鼻子', '左眼內角', '左眼中心', '左眼外角', '右眼內角', '右眼中心', '右眼外角',
            '左耳', '右耳', '嘴巴左角', '嘴巴右角', '左肩膀', '右肩膀', '左手肘', '右手肘',
            '左手腕', '右手腕', '左手小指', '右手小指', '左手食指', '右手食指', '左手拇指', '右手拇指',
            '左臀部', '右臀部', '左膝蓋', '右膝蓋', '左腳踝', '右腳踝', '左腳跟', '右腳跟', '左腳趾', '右腳趾'
        ]

        # 提取33個關鍵點
        for i, name in enumerate(landmark_names):
            x_col = f'{name}_X座標'
            y_col = f'{name}_Y座標'
            z_col = f'{name}_Z座標'

            if x_col in row and y_col in row and z_col in row:
                x = float(row[x_col]) if pd.notna(row[x_col]) else 0.0
                y = float(row[y_col]) if pd.notna(row[y_col]) else 0.0
                z = float(row[z_col]) if pd.notna(row[z_col]) else 0.0

                landmarks.landmark.append(MockLandmark(x, y, z))
            else:
                landmarks.landmark.append(MockLandmark(0.0, 0.0, 0.0))

        return landmarks

    except Exception as e:
        print(f"⚠️ 提取關鍵點失敗: {e}")
        return None

def generate_test_report(test_results, screw_detection_log, completed_screws, original_df):
    """生成測試報告"""
    print("\n" + "="*80)
    print("🔧 螺絲監控系統測試報告")
    print("="*80)

    # 整體統計
    total_frames = len(test_results)
    detection_frames = len([r for r in test_results if r['detected_screw'] is not None])

    print(f"\n📊 整體統計:")
    print(f"  總處理幀數: {total_frames}")
    print(f"  螺絲檢測幀數: {detection_frames} ({detection_frames/total_frames*100:.1f}%)")
    print(f"  檢測到完成螺絲: {len(completed_screws)} 顆")
    print(f"  完成螺絲編號: {sorted(completed_screws)}")

    # 分析實際標籤 vs 檢測結果
    print(f"\n🎯 螺絲檢測準確性分析:")

    # 統計實際的螺絲標籤
    actual_screws = set()
    for _, row in original_df.iterrows():
        label = row.get('標籤', '')
        if '鎖第' in label and '螺絲' in label:
            try:
                screw_num = int(label.split('鎖第')[1].split('顆螺絲')[0])
                actual_screws.add(screw_num)
            except:
                pass

    print(f"  實際螺絲數量: {len(actual_screws)} 顆")
    print(f"  實際螺絲編號: {sorted(actual_screws)}")
    print(f"  檢測覆蓋率: {len(completed_screws)/len(actual_screws)*100:.1f}%" if actual_screws else "無法計算")

    # 狀態分布統計
    state_counts = {}
    for result in test_results:
        state = result['state']
        state_counts[state] = state_counts.get(state, 0) + 1

    print(f"\n🔄 狀態分布:")
    for state, count in state_counts.items():
        print(f"  {state}: {count} 幀 ({count/total_frames*100:.1f}%)")

    # 螺絲檢測詳細記錄
    if screw_detection_log:
        print(f"\n🔩 螺絲檢測詳細記錄 (前20條):")
        print(f"{'幀數':<8} {'時間':<12} {'實際標籤':<15} {'檢測螺絲':<8} {'狀態':<12}")
        print("-" * 70)

        for i, log in enumerate(screw_detection_log[:20]):
            print(f"{log['frame']:<8} {log['time']:<12} {log['actual_label']:<15} "
                  f"#{log['detected_screw']:<7} {log['state']:<12}")

        if len(screw_detection_log) > 20:
            print(f"  ... 還有 {len(screw_detection_log) - 20} 條記錄")

    # 保存詳細結果
    results_df = pd.DataFrame(test_results)
    results_df.to_csv('screw_monitor_test_results.csv', index=False, encoding='utf-8-sig')
    print(f"\n💾 詳細測試結果已保存到: screw_monitor_test_results.csv")

    # 生成可視化
    create_test_visualization(test_results, original_df)

def create_test_visualization(test_results, original_df):
    """創建測試結果可視化"""
    try:
        import matplotlib.pyplot as plt

        # 設置中文字體
        plt.rcParams['font.sans-serif'] = ['Microsoft JhengHei', 'SimHei']
        plt.rcParams['axes.unicode_minus'] = False
    except ImportError:
        print("⚠️ matplotlib未安裝，跳過可視化生成")
        return

    fig, axes = plt.subplots(3, 1, figsize=(15, 12))

    # 提取數據
    frames = [r['frame'] for r in test_results]
    detected_screws = [r['detected_screw'] if r['detected_screw'] else 0 for r in test_results]
    states = [r['state'] for r in test_results]
    force_probs = [r['force_probability'] for r in test_results]

    # 圖1: 螺絲檢測結果
    axes[0].scatter(frames, detected_screws, c='red', alpha=0.6, s=10)
    axes[0].set_ylabel('檢測到的螺絲編號')
    axes[0].set_title('螺絲位置檢測結果')
    axes[0].grid(True, alpha=0.3)
    axes[0].set_ylim(0, 17)

    # 圖2: 狀態變化
    state_mapping = {'idle': 0, 'approaching': 1, 'engaging': 2, 'rotating': 3, 'complete': 4}
    state_values = [state_mapping.get(s, 0) for s in states]

    axes[1].plot(frames, state_values, 'b-', alpha=0.7, linewidth=1)
    axes[1].set_ylabel('系統狀態')
    axes[1].set_title('螺絲監控狀態變化')
    axes[1].set_yticks(list(state_mapping.values()))
    axes[1].set_yticklabels(list(state_mapping.keys()))
    axes[1].grid(True, alpha=0.3)

    # 圖3: 出力機率對比
    axes[2].plot(frames, force_probs, 'g-', alpha=0.7, linewidth=1, label='出力機率')
    axes[2].axhline(y=0.5, color='orange', linestyle='--', alpha=0.7, label='50%閾值')
    axes[2].set_ylabel('出力機率')
    axes[2].set_xlabel('幀數')
    axes[2].set_title('出力檢測機率變化')
    axes[2].legend()
    axes[2].grid(True, alpha=0.3)
    axes[2].set_ylim(0, 1)

    plt.tight_layout()
    plt.savefig('screw_monitor_test_visualization.png', dpi=300, bbox_inches='tight')
    plt.close()

    print(f"📈 測試可視化圖表已保存到: screw_monitor_test_visualization.png")

# 使用示例
def main():
    # CSV測試模式
    import os

    # 檢查檔案路徑
    possible_paths = [
        'force_detection/force_analysis_result.csv',
        'force_analysis_result.csv',
        '1-pose_output/1_pose_data_繁體中文_清理版_含時間_含標籤.csv'
    ]

    csv_file = None
    for path in possible_paths:
        if os.path.exists(path):
            csv_file = path
            print(f"✅ 找到CSV檔案: {path}")
            break

    if csv_file is None:
        print("❌ 找不到CSV檔案，請檢查路徑")
        return

    test_results = test_with_csv_data(csv_file)

    print("\n🎉 CSV測試完成！")
    print("📊 查看生成的檔案:")
    print("  - screw_monitor_test_results.csv (詳細結果)")
    print("  - screw_monitor_test_visualization.png (可視化圖表)")

if __name__ == "__main__":
    main()