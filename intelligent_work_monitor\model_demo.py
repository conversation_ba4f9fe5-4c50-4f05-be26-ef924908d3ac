#!/usr/bin/env python3
"""
LSTM模型測試展示程式
展示訓練好的模型在實際數據上的預測效果
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import pickle
import os

try:
    import tensorflow as tf
    from tensorflow.keras.models import load_model
    TF_AVAILABLE = True
except ImportError:
    print("❌ TensorFlow不可用")
    TF_AVAILABLE = False

class ModelDemo:
    """模型展示類"""
    
    def __init__(self):
        self.model = None
        self.label_encoder = None
        self.scaler = None
        self.config = None
        
        # 設置中文字體
        plt.rcParams['font.sans-serif'] = ['Microsoft JhengHei', 'SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        
    def load_trained_model(self):
        """載入訓練好的模型和相關檔案"""
        print("🤖 載入訓練好的模型...")
        
        try:
            # 載入模型
            if os.path.exists('models/lstm_action_model.h5'):
                self.model = load_model('models/lstm_action_model.h5')
                print("✅ LSTM模型載入成功")
            else:
                print("❌ 找不到模型檔案")
                return False
            
            # 載入標籤編碼器
            if os.path.exists('models/label_encoder.pkl'):
                with open('models/label_encoder.pkl', 'rb') as f:
                    self.label_encoder = pickle.load(f)
                print("✅ 標籤編碼器載入成功")
            
            # 載入特徵標準化器
            if os.path.exists('models/scaler.pkl'):
                with open('models/scaler.pkl', 'rb') as f:
                    self.scaler = pickle.load(f)
                print("✅ 特徵標準化器載入成功")
            
            # 載入配置
            if os.path.exists('models/config.json'):
                import json
                with open('models/config.json', 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                print("✅ 模型配置載入成功")
            
            return True
            
        except Exception as e:
            print(f"❌ 模型載入失敗: {e}")
            return False
    
    def prepare_test_data(self, csv_file):
        """準備測試數據"""
        print(f"📊 準備測試數據: {csv_file}")
        
        # 載入CSV
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        print(f"✅ 載入 {len(df)} 行數據")
        
        # 提取特徵 (與訓練時相同的8個關鍵點)
        key_landmarks = ['左肩膀', '右肩膀', '左手肘', '右手肘', '左手腕', '右手腕', '左手食指', '右手食指']
        
        feature_columns = []
        for name in key_landmarks:
            x_col = f'{name}_X座標'
            y_col = f'{name}_Y座標'
            z_col = f'{name}_Z座標'
            
            if x_col in df.columns and y_col in df.columns and z_col in df.columns:
                feature_columns.extend([x_col, y_col, z_col])
        
        print(f"📋 使用特徵: {len(feature_columns)} 個")
        
        # 提取特徵和標籤
        features = df[feature_columns].values
        true_labels = df['標籤'].values
        
        # 特徵標準化
        if self.scaler:
            scaled_features = self.scaler.transform(features)
        else:
            scaled_features = features
        
        return scaled_features, true_labels, df
    
    def create_sequences(self, features, sequence_length=30):
        """創建時序序列"""
        sequences = []
        
        for i in range(len(features) - sequence_length + 1):
            sequence = features[i:i + sequence_length]
            sequences.append(sequence)
        
        return np.array(sequences)
    
    def predict_actions(self, test_data_file):
        """預測動作並生成展示結果"""
        print("🎯 開始動作預測展示...")
        
        # 準備數據
        features, true_labels, df = self.prepare_test_data(test_data_file)
        
        # 創建序列
        sequence_length = self.config.get('sequence_length', 30) if self.config else 30
        sequences = self.create_sequences(features, sequence_length)
        
        print(f"🔄 生成 {len(sequences)} 個測試序列")
        
        # 模型預測
        print("🤖 模型預測中...")
        predictions = self.model.predict(sequences, verbose=1)
        predicted_classes = np.argmax(predictions, axis=1)
        
        # 解碼預測結果
        if self.label_encoder:
            predicted_labels = self.label_encoder.inverse_transform(predicted_classes)
        else:
            predicted_labels = predicted_classes
        
        # 對應的真實標籤 (取序列最後一幀的標籤)
        true_sequence_labels = true_labels[sequence_length-1:]
        
        # 計算準確率
        if self.label_encoder:
            true_encoded = self.label_encoder.transform(true_sequence_labels)
            accuracy = np.mean(predicted_classes == true_encoded)
        else:
            accuracy = np.mean(predicted_labels == true_sequence_labels)
        
        print(f"🎯 測試準確率: {accuracy:.4f} ({accuracy*100:.2f}%)")
        
        # 生成詳細結果
        results = []
        for i in range(len(sequences)):
            frame_idx = i + sequence_length - 1
            result = {
                'frame': frame_idx,
                'time': df.iloc[frame_idx]['影片時間'] if '影片時間' in df.columns else f"{frame_idx/30:.1f}s",
                'true_label': true_sequence_labels[i],
                'predicted_label': predicted_labels[i],
                'confidence': np.max(predictions[i]),
                'correct': predicted_labels[i] == true_sequence_labels[i]
            }
            results.append(result)
        
        return results, accuracy
    
    def create_demo_visualization(self, results):
        """創建展示可視化"""
        print("📈 生成展示可視化...")
        
        # 轉換為DataFrame
        df_results = pd.DataFrame(results)
        
        # 創建大型展示圖
        fig, axes = plt.subplots(4, 1, figsize=(20, 16))
        
        # 圖1: 預測準確性時間軸
        frames = df_results['frame'].values
        correct = df_results['correct'].values
        
        colors = ['red' if not c else 'green' for c in correct]
        axes[0].scatter(frames, correct, c=colors, alpha=0.6, s=20)
        axes[0].set_ylabel('預測正確性')
        axes[0].set_title('LSTM模型預測準確性時間軸', fontsize=16, fontweight='bold')
        axes[0].set_ylim(-0.1, 1.1)
        axes[0].grid(True, alpha=0.3)
        
        # 圖2: 預測信心度
        confidence = df_results['confidence'].values
        axes[1].plot(frames, confidence, 'b-', alpha=0.7, linewidth=1)
        axes[1].axhline(y=0.8, color='orange', linestyle='--', alpha=0.7, label='高信心度閾值')
        axes[1].set_ylabel('預測信心度')
        axes[1].set_title('模型預測信心度變化', fontsize=14)
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        axes[1].set_ylim(0, 1)
        
        # 圖3: 動作類別分布
        true_labels = df_results['true_label'].values
        pred_labels = df_results['predicted_label'].values
        
        # 統計各動作的預測準確率
        action_accuracy = {}
        for true_label in set(true_labels):
            mask = true_labels == true_label
            if np.sum(mask) > 0:
                acc = np.mean(pred_labels[mask] == true_label)
                action_accuracy[true_label] = acc
        
        # 繪製動作準確率條形圖
        actions = list(action_accuracy.keys())
        accuracies = list(action_accuracy.values())
        
        bars = axes[2].bar(range(len(actions)), accuracies, 
                          color=['green' if acc > 0.9 else 'orange' if acc > 0.7 else 'red' for acc in accuracies])
        axes[2].set_ylabel('預測準確率')
        axes[2].set_title('各動作類別預測準確率', fontsize=14)
        axes[2].set_xticks(range(len(actions)))
        axes[2].set_xticklabels(actions, rotation=45, ha='right')
        axes[2].set_ylim(0, 1)
        axes[2].grid(True, alpha=0.3)
        
        # 在條形圖上添加數值
        for i, (bar, acc) in enumerate(zip(bars, accuracies)):
            axes[2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                        f'{acc:.2f}', ha='center', va='bottom', fontweight='bold')
        
        # 圖4: 混淆矩陣熱圖 (簡化版)
        from sklearn.metrics import confusion_matrix
        
        # 只顯示主要動作類別
        main_actions = ['尋位', '鎖第1顆螺絲', '鎖第2顆螺絲', '鎖第3顆螺絲', '旋轉工件(順時針)']
        
        # 過濾數據
        mask = np.isin(true_labels, main_actions) & np.isin(pred_labels, main_actions)
        if np.sum(mask) > 0:
            filtered_true = true_labels[mask]
            filtered_pred = pred_labels[mask]
            
            cm = confusion_matrix(filtered_true, filtered_pred, labels=main_actions)
            
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                       xticklabels=main_actions, yticklabels=main_actions, ax=axes[3])
            axes[3].set_title('主要動作混淆矩陣', fontsize=14)
            axes[3].set_xlabel('預測標籤')
            axes[3].set_ylabel('真實標籤')
        
        plt.tight_layout()
        plt.savefig('results/model_demo_visualization.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("📈 展示可視化已保存: results/model_demo_visualization.png")
    
    def generate_demo_report(self, results, accuracy):
        """生成展示報告"""
        print("📋 生成展示報告...")
        
        df_results = pd.DataFrame(results)
        
        # 統計信息
        total_predictions = len(results)
        correct_predictions = sum(r['correct'] for r in results)
        
        # 各動作統計
        action_stats = {}
        for result in results:
            action = result['true_label']
            if action not in action_stats:
                action_stats[action] = {'total': 0, 'correct': 0}
            action_stats[action]['total'] += 1
            if result['correct']:
                action_stats[action]['correct'] += 1
        
        # 生成報告
        report = f"""
# 🤖 LSTM動作識別模型展示報告

## 📊 整體性能
- **測試樣本數**: {total_predictions:,} 個
- **預測準確率**: {accuracy:.4f} ({accuracy*100:.2f}%)
- **正確預測**: {correct_predictions:,} 個
- **錯誤預測**: {total_predictions - correct_predictions:,} 個

## 🎯 各動作識別準確率

| 動作類別 | 樣本數 | 正確數 | 準確率 |
|---------|--------|--------|--------|
"""
        
        for action, stats in sorted(action_stats.items()):
            acc = stats['correct'] / stats['total'] if stats['total'] > 0 else 0
            report += f"| {action} | {stats['total']} | {stats['correct']} | {acc:.2%} |\n"
        
        report += f"""
## 📈 性能分析

### ✅ 優秀表現 (準確率 > 90%)
"""
        excellent = [action for action, stats in action_stats.items() 
                    if stats['correct'] / stats['total'] > 0.9 and stats['total'] > 0]
        for action in excellent:
            stats = action_stats[action]
            acc = stats['correct'] / stats['total']
            report += f"- **{action}**: {acc:.1%} ({stats['correct']}/{stats['total']})\n"
        
        report += f"""
### 🟡 良好表現 (準確率 70-90%)
"""
        good = [action for action, stats in action_stats.items() 
               if 0.7 < stats['correct'] / stats['total'] <= 0.9 and stats['total'] > 0]
        for action in good:
            stats = action_stats[action]
            acc = stats['correct'] / stats['total']
            report += f"- **{action}**: {acc:.1%} ({stats['correct']}/{stats['total']})\n"
        
        report += f"""
### 🔴 需要改進 (準確率 < 70%)
"""
        poor = [action for action, stats in action_stats.items() 
               if stats['correct'] / stats['total'] < 0.7 and stats['total'] > 0]
        for action in poor:
            stats = action_stats[action]
            acc = stats['correct'] / stats['total']
            report += f"- **{action}**: {acc:.1%} ({stats['correct']}/{stats['total']})\n"
        
        if not poor:
            report += "- 🎉 所有動作都達到良好以上的識別準確率！\n"
        
        report += f"""
## 🚀 模型特點

### 技術規格
- **模型類型**: 雙向LSTM + 批次正規化
- **序列長度**: 30幀 (約1秒)
- **特徵維度**: 24維 (8個關鍵點 × 3座標)
- **動作類別**: {len(action_stats)} 種

### 實際應用
- **實時監控**: 可用於即時作業監控
- **品質檢測**: 自動識別操作異常
- **培訓輔助**: 協助新員工學習標準動作
- **效率分析**: 統計作業時間和效率

## 📋 詳細預測結果 (前20個)

| 幀數 | 時間 | 真實動作 | 預測動作 | 信心度 | 正確性 |
|------|------|----------|----------|--------|--------|
"""
        
        for i, result in enumerate(results[:20]):
            correct_mark = "✅" if result['correct'] else "❌"
            report += f"| {result['frame']} | {result['time']} | {result['true_label']} | {result['predicted_label']} | {result['confidence']:.3f} | {correct_mark} |\n"
        
        if len(results) > 20:
            report += f"\n... 還有 {len(results) - 20} 個預測結果\n"
        
        report += f"""
---

**報告生成時間**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**模型檔案**: models/lstm_action_model.h5
**測試數據**: data/1_pose_data_繁體中文_清理版_含時間_含標籤.csv
"""
        
        # 保存報告
        with open('results/model_demo_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("📋 展示報告已保存: results/model_demo_report.md")
        
        return report

def main():
    """主函數"""
    print("🎬 LSTM模型展示程式啟動...")
    print("="*60)
    
    # 創建展示器
    demo = ModelDemo()
    
    # 載入模型
    if not demo.load_trained_model():
        print("❌ 模型載入失敗，無法進行展示")
        return
    
    # 測試數據檔案
    test_file = 'data/1_pose_data_繁體中文_清理版_含時間_含標籤.csv'
    
    if not os.path.exists(test_file):
        print(f"❌ 找不到測試數據: {test_file}")
        return
    
    # 進行預測展示
    results, accuracy = demo.predict_actions(test_file)
    
    # 生成可視化
    demo.create_demo_visualization(results)
    
    # 生成報告
    report = demo.generate_demo_report(results, accuracy)
    
    print("\n" + "="*60)
    print("🎉 模型展示完成！")
    print("📁 生成的展示檔案:")
    print("  - results/model_demo_visualization.png (可視化圖表)")
    print("  - results/model_demo_report.md (詳細報告)")
    print(f"  - 整體準確率: {accuracy*100:.2f}%")

if __name__ == "__main__":
    main()
