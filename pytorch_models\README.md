# PyTorch LSTM 模型

## 模型資訊
- **模型類型**: 改進的LSTM (ImprovedSOPLSTM)
- **準確率**: 96.89%
- **訓練數據**: 特徵工程後的數據 (35個特徵)
- **序列長度**: 15幀
- **問題**: 嚴重過擬合

## 檔案說明
- `best_sop_pytorch_model.pth` - 最佳模型檔案
- `feature_scaler_pytorch.pkl` - 特徵縮放器
- `label_encoder_pytorch.pkl` - 標籤編碼器
- `模型摘要.md` - 詳細模型資訊

## 模型特點
- ✅ 訓練準確率很高 (96.89%)
- ❌ 嚴重過擬合問題
- ❌ 99.3%預測為同一動作
- ❌ 無法區分不同螺絲動作

## 預測結果 (視頻2)
- 鎖第9顆螺絲: 99.3%
- 其他動作: 0.4%
- 鎖第7顆螺絲: 0.3%
- 平均信心度: 0.910 (過度自信)

## 模型架構
- 雙向LSTM + 注意力機制
- 批次正規化
- 多層分類器
- Dropout正規化

## 使用方法
```python
# 預測視頻2
python pytorch_inference.py predict_csv pose_output/2_sop_features_已標記_繁體中文.csv pose_output/video2_predictions.csv
```

## 問題分析
此模型雖然訓練準確率高，但實際應用效果差，主要原因：
1. 過度學習訓練數據的特定模式
2. 無法泛化到新數據
3. 特徵工程可能過於複雜
4. 需要更好的正規化策略
