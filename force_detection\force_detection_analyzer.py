#!/usr/bin/env python3
"""
出力檢測分析器
通過分析MediaPipe骨架XYZ座標向量來判斷員工是否有出力鎖螺絲
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import signal
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import warnings
warnings.filterwarnings('ignore')

# 設置中文字體
plt.rcParams['font.sans-serif'] = ['Microsoft JhengHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

class ForceDetectionAnalyzer:
    """出力檢測分析器"""
    
    def __init__(self, detection_mode='standard'):
        """
        初始化出力檢測分析器

        detection_mode 選項:
        - 'minimal': 最少關鍵點 (3個) - 右手腕、右手肘、右肩膀
        - 'standard': 標準模式 (11個) - 預設
        - 'comprehensive': 全面模式 (20個) - 包含更多關鍵點
        - 'custom': 自定義模式 - 可手動指定
        """

        self.detection_mode = detection_mode
        self.key_landmarks = self._get_landmarks_by_mode(detection_mode)
        
        # 出力檢測特徵
        self.features = []
        self.feature_names = []

        # 模型
        self.scaler = StandardScaler()
        self.classifier = RandomForestClassifier(n_estimators=100, random_state=42)

    def _get_landmarks_by_mode(self, mode):
        """根據模式選擇關鍵點"""

        if mode == 'minimal':
            # 最少關鍵點 - 只關注主要出力部位
            return {
                '右手腕': 16,    # 最重要 - 直接接觸工具
                '右手肘': 14,    # 重要 - 手臂角度
                '右肩膀': 12     # 重要 - 力量支撐
            }

        elif mode == 'standard':
            # 標準模式 - 平衡效果和效率
            return {
                '左肩膀': 11, '右肩膀': 12,
                '左手肘': 13, '右手肘': 14,
                '左手腕': 15, '右手腕': 16,
                '左手食指': 19, '右手食指': 20,
                '左臀部': 23, '右臀部': 24,
                '鼻子': 0
            }

        elif mode == 'comprehensive':
            # 全面模式 - 包含更多關鍵點
            return {
                '鼻子': 0,
                '左眼內角': 1, '右眼內角': 4,
                '左耳': 7, '右耳': 8,
                '左肩膀': 11, '右肩膀': 12,
                '左手肘': 13, '右手肘': 14,
                '左手腕': 15, '右手腕': 16,
                '左手小指': 17, '右手小指': 18,
                '左手食指': 19, '右手食指': 20,
                '左手拇指': 21, '右手拇指': 22,
                '左臀部': 23, '右臀部': 24,
                '左膝蓋': 25, '右膝蓋': 26,
                '左腳踝': 27, '右腳踝': 28
            }

        elif mode == 'custom':
            # 自定義模式 - 可以手動修改
            return {
                '右手腕': 16,
                '右手肘': 14,
                '右肩膀': 12,
                '右手食指': 20,
                '右手拇指': 22
            }

        else:
            # 預設使用標準模式
            return self._get_landmarks_by_mode('standard')
        
    def load_pose_data(self, csv_file):
        """載入姿勢數據"""
        print(f"📊 載入姿勢數據: {csv_file}")

        try:
            df = pd.read_csv(csv_file, encoding='utf-8-sig')
            print(f"✅ 成功載入 {len(df)} 行數據，{len(df.columns)} 個欄位")
            print(f"📋 前5個欄位: {list(df.columns[:5])}")
            return df
        except Exception as e:
            print(f"❌ 載入檔案失敗: {e}")
            raise
    
    def extract_coordinates(self, df):
        """提取關鍵點座標"""
        print("🔍 提取關鍵點座標...")
        print(f"🎯 檢測模式: {self.detection_mode}")
        print(f"📍 目標關鍵點: {list(self.key_landmarks.keys())}")

        coordinates = {}
        missing_landmarks = []

        for name, idx in self.key_landmarks.items():
            x_col = f'{name}_X座標'
            y_col = f'{name}_Y座標'
            z_col = f'{name}_Z座標'

            if x_col in df.columns and y_col in df.columns and z_col in df.columns:
                coordinates[name] = {
                    'x': df[x_col].values,
                    'y': df[y_col].values,
                    'z': df[z_col].values
                }
                print(f"✅ 找到 {name} 座標")
            else:
                missing_landmarks.append(name)
                print(f"⚠️ 找不到 {name} 的座標欄位")

        if missing_landmarks:
            print(f"❌ 缺少關鍵點: {missing_landmarks}")
            print("📋 可用的欄位名稱:")
            landmark_columns = [col for col in df.columns if '_X座標' in col or '_Y座標' in col or '_Z座標' in col]
            for col in landmark_columns[:10]:  # 只顯示前10個
                print(f"  {col}")
            if len(landmark_columns) > 10:
                print(f"  ... 還有 {len(landmark_columns) - 10} 個")

        print(f"✅ 成功提取 {len(coordinates)} 個關鍵點座標")
        return coordinates
    
    def calculate_joint_angles(self, coordinates):
        """計算關節角度"""
        print("📐 計算關節角度...")
        
        angles = {}
        
        # 右手肘角度 (肩膀-肘-手腕)
        if all(joint in coordinates for joint in ['右肩膀', '右手肘', '右手腕']):
            angles['右手肘角度'] = self._calculate_angle_3d(
                coordinates['右肩膀'], coordinates['右手肘'], coordinates['右手腕']
            )
        
        # 左手肘角度
        if all(joint in coordinates for joint in ['左肩膀', '左手肘', '左手腕']):
            angles['左手肘角度'] = self._calculate_angle_3d(
                coordinates['左肩膀'], coordinates['左手肘'], coordinates['左手腕']
            )
        
        # 身體前傾角度 (肩膀中心-臀部中心-垂直線)
        if all(joint in coordinates for joint in ['左肩膀', '右肩膀', '左臀部', '右臀部']):
            shoulder_center = self._get_midpoint(coordinates['左肩膀'], coordinates['右肩膀'])
            hip_center = self._get_midpoint(coordinates['左臀部'], coordinates['右臀部'])
            angles['身體前傾角度'] = self._calculate_lean_angle(shoulder_center, hip_center)
        
        print(f"✅ 計算了 {len(angles)} 個關節角度")
        return angles
    
    def calculate_motion_features(self, coordinates):
        """計算動作特徵"""
        print("🏃 計算動作特徵...")
        
        features = {}
        
        for name, coords in coordinates.items():
            # 速度 (位置變化率)
            velocity_x = np.gradient(coords['x'])
            velocity_y = np.gradient(coords['y'])
            velocity_z = np.gradient(coords['z'])
            velocity_magnitude = np.sqrt(velocity_x**2 + velocity_y**2 + velocity_z**2)
            
            # 加速度 (速度變化率)
            acceleration_x = np.gradient(velocity_x)
            acceleration_y = np.gradient(velocity_y)
            acceleration_z = np.gradient(velocity_z)
            acceleration_magnitude = np.sqrt(acceleration_x**2 + acceleration_y**2 + acceleration_z**2)
            
            # 抖動程度 (位置標準差)
            jitter = np.sqrt(np.var(coords['x']) + np.var(coords['y']) + np.var(coords['z']))
            
            features[f'{name}_速度'] = velocity_magnitude
            features[f'{name}_加速度'] = acceleration_magnitude
            features[f'{name}_抖動'] = jitter
        
        print(f"✅ 計算了 {len(features)} 個動作特徵")
        return features
    
    def calculate_stability_features(self, coordinates, window_size=10):
        """計算穩定性特徵"""
        print("⚖️ 計算穩定性特徵...")
        
        stability_features = {}
        
        for name, coords in coordinates.items():
            # 滑動窗口穩定性
            stability_scores = []
            
            for i in range(len(coords['x']) - window_size + 1):
                window_x = coords['x'][i:i+window_size]
                window_y = coords['y'][i:i+window_size]
                window_z = coords['z'][i:i+window_size]
                
                # 計算窗口內的標準差 (越小越穩定)
                stability = np.sqrt(np.var(window_x) + np.var(window_y) + np.var(window_z))
                stability_scores.append(stability)
            
            # 補齊長度
            stability_scores = [stability_scores[0]] * (window_size-1) + stability_scores
            
            stability_features[f'{name}_穩定性'] = np.array(stability_scores)
        
        print(f"✅ 計算了 {len(stability_features)} 個穩定性特徵")
        return stability_features
    
    def detect_force_application(self, df, coordinates, angles, motion_features, stability_features):
        """檢測出力狀態"""
        print("💪 檢測出力狀態...")
        
        # 創建特徵矩陣
        feature_matrix = []
        feature_names = []
        
        # 添加角度特徵
        for angle_name, angle_values in angles.items():
            feature_matrix.append(angle_values)
            feature_names.append(angle_name)
        
        # 添加關鍵點的動作特徵 (只選擇手部相關)
        key_joints = ['右手腕', '右手肘', '右肩膀']
        for joint in key_joints:
            if f'{joint}_速度' in motion_features:
                feature_matrix.append(motion_features[f'{joint}_速度'])
                feature_names.append(f'{joint}_速度')
            
            if f'{joint}_加速度' in motion_features:
                feature_matrix.append(motion_features[f'{joint}_加速度'])
                feature_names.append(f'{joint}_加速度')
            
            if f'{joint}_穩定性' in stability_features:
                feature_matrix.append(stability_features[f'{joint}_穩定性'])
                feature_names.append(f'{joint}_穩定性')
        
        # 轉置矩陣 (每行是一個時間點，每列是一個特徵)
        feature_matrix = np.array(feature_matrix).T
        
        # 基於規則的出力檢測
        force_labels = self._rule_based_force_detection(
            angles, motion_features, stability_features, df
        )
        
        self.features = feature_matrix
        self.feature_names = feature_names
        
        print(f"✅ 生成了 {feature_matrix.shape[1]} 個特徵")
        print(f"✅ 檢測到 {np.sum(force_labels)} 個出力時間點")
        
        return force_labels
    
    def _rule_based_force_detection(self, angles, motion_features, stability_features, df):
        """基於規則的出力檢測"""
        
        force_labels = np.zeros(len(df), dtype=bool)
        
        # 規則1: 手腕穩定性增加 + 速度降低 = 可能在用力
        if '右手腕_穩定性' in stability_features and '右手腕_速度' in motion_features:
            stability = stability_features['右手腕_穩定性']
            velocity = motion_features['右手腕_速度']
            
            # 穩定性閾值 (低於平均值表示更穩定)
            stability_threshold = np.percentile(stability, 30)
            velocity_threshold = np.percentile(velocity, 40)
            
            rule1 = (stability < stability_threshold) & (velocity < velocity_threshold)
            force_labels |= rule1
        
        # 規則2: 手肘角度在特定範圍 (用力時手肘會保持特定角度)
        if '右手肘角度' in angles:
            elbow_angle = angles['右手肘角度']
            # 用力時手肘角度通常在60-120度之間
            rule2 = (elbow_angle > 60) & (elbow_angle < 120)
            force_labels |= rule2
        
        # 規則3: 結合標籤信息 (如果有的話)
        if '標籤' in df.columns:
            screw_labels = df['標籤'].str.contains('鎖第.*螺絲', na=False)
            force_labels |= screw_labels.values
        
        return force_labels
    
    def train_force_classifier(self, force_labels):
        """訓練出力分類器"""
        print("🤖 訓練出力分類器...")
        
        if len(self.features) == 0:
            print("❌ 沒有特徵數據，無法訓練")
            return False
        
        # 處理缺失值
        features_clean = np.nan_to_num(self.features)
        
        # 標準化特徵
        features_scaled = self.scaler.fit_transform(features_clean)
        
        # 分割訓練和測試數據
        X_train, X_test, y_train, y_test = train_test_split(
            features_scaled, force_labels, test_size=0.3, random_state=42
        )
        
        # 訓練分類器
        self.classifier.fit(X_train, y_train)
        
        # 評估模型
        y_pred = self.classifier.predict(X_test)
        
        print("\n📊 分類器性能評估:")
        print(classification_report(y_test, y_pred, target_names=['無出力', '有出力']))
        
        # 特徵重要性
        feature_importance = self.classifier.feature_importances_
        importance_df = pd.DataFrame({
            '特徵': self.feature_names,
            '重要性': feature_importance
        }).sort_values('重要性', ascending=False)
        
        print("\n🔍 特徵重要性排序:")
        print(importance_df.head(10))
        
        return True
    
    def predict_force_application(self, features=None):
        """預測出力狀態"""
        if features is None:
            features = self.features
        
        features_clean = np.nan_to_num(features)
        features_scaled = self.scaler.transform(features_clean)
        
        predictions = self.classifier.predict(features_scaled)
        probabilities = self.classifier.predict_proba(features_scaled)[:, 1]
        
        return predictions, probabilities
    
    def visualize_force_detection(self, df, force_labels, predictions=None):
        """視覺化出力檢測結果"""
        print("📈 生成視覺化圖表...")

        try:
            fig, axes = plt.subplots(3, 1, figsize=(15, 12))

            # 時間軸
            time_axis = range(len(df))

            # 圖1: 出力檢測結果
            axes[0].plot(time_axis, force_labels.astype(int), 'b-', label='規則檢測', alpha=0.7)
            if predictions is not None:
                axes[0].plot(time_axis, predictions.astype(int) + 0.1, 'r-', label='ML預測', alpha=0.7)
            axes[0].set_ylabel('出力狀態')
            axes[0].set_title('出力檢測結果')
            axes[0].legend()
            axes[0].grid(True, alpha=0.3)

            # 圖2: 手腕穩定性
            if hasattr(self, 'stability_features') and '右手腕_穩定性' in self.stability_features:
                stability = self.stability_features['右手腕_穩定性']
                axes[1].plot(time_axis, stability, 'g-', alpha=0.7)
                axes[1].set_ylabel('手腕穩定性')
                axes[1].set_title('手腕穩定性變化')
                axes[1].grid(True, alpha=0.3)
            else:
                axes[1].text(0.5, 0.5, '無手腕穩定性數據', ha='center', va='center', transform=axes[1].transAxes)
                axes[1].set_title('手腕穩定性變化 (無數據)')

            # 圖3: 手肘角度
            if hasattr(self, 'angles') and '右手肘角度' in self.angles:
                elbow_angle = self.angles['右手肘角度']
                axes[2].plot(time_axis, elbow_angle, 'm-', alpha=0.7)
                axes[2].set_ylabel('手肘角度 (度)')
                axes[2].set_xlabel('幀數')
                axes[2].set_title('右手肘角度變化')
                axes[2].grid(True, alpha=0.3)
            else:
                axes[2].text(0.5, 0.5, '無手肘角度數據', ha='center', va='center', transform=axes[2].transAxes)
                axes[2].set_title('右手肘角度變化 (無數據)')
                axes[2].set_xlabel('幀數')

            plt.tight_layout()
            plt.savefig('force_detection_analysis.png', dpi=300, bbox_inches='tight')

            # 不顯示圖表，只保存
            plt.close()

            print("✅ 圖表已保存為 force_detection_analysis.png")

        except Exception as e:
            print(f"⚠️ 視覺化生成失敗: {e}")
            print("📊 將跳過圖表生成，繼續其他分析...")
    
    def _calculate_angle_3d(self, point1, point2, point3):
        """計算3D空間中三點形成的角度"""
        angles = []
        
        for i in range(len(point1['x'])):
            # 向量1: point2 -> point1
            v1 = np.array([
                point1['x'][i] - point2['x'][i],
                point1['y'][i] - point2['y'][i],
                point1['z'][i] - point2['z'][i]
            ])
            
            # 向量2: point2 -> point3
            v2 = np.array([
                point3['x'][i] - point2['x'][i],
                point3['y'][i] - point2['y'][i],
                point3['z'][i] - point2['z'][i]
            ])
            
            # 計算角度
            cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2) + 1e-8)
            cos_angle = np.clip(cos_angle, -1, 1)  # 防止數值誤差
            angle = np.degrees(np.arccos(cos_angle))
            
            angles.append(angle)
        
        return np.array(angles)
    
    def _get_midpoint(self, point1, point2):
        """計算兩點中點"""
        return {
            'x': (point1['x'] + point2['x']) / 2,
            'y': (point1['y'] + point2['y']) / 2,
            'z': (point1['z'] + point2['z']) / 2
        }
    
    def _calculate_lean_angle(self, shoulder_center, hip_center):
        """計算身體前傾角度"""
        angles = []
        
        for i in range(len(shoulder_center['x'])):
            # 身體向量 (臀部到肩膀)
            body_vector = np.array([
                shoulder_center['x'][i] - hip_center['x'][i],
                shoulder_center['y'][i] - hip_center['y'][i],
                0  # 忽略Z軸，只看前後傾斜
            ])
            
            # 垂直向量
            vertical_vector = np.array([0, -1, 0])  # Y軸向下為垂直
            
            # 計算角度
            cos_angle = np.dot(body_vector, vertical_vector) / (np.linalg.norm(body_vector) + 1e-8)
            cos_angle = np.clip(cos_angle, -1, 1)
            angle = np.degrees(np.arccos(abs(cos_angle)))
            
            angles.append(angle)
        
        return np.array(angles)

def main():
    """主函數"""
    import argparse

    parser = argparse.ArgumentParser(description='出力檢測分析器')
    parser.add_argument('--input', '-i', type=str, required=True,
                       help='輸入的姿勢數據CSV檔案')
    parser.add_argument('--output', '-o', type=str,
                       help='輸出結果CSV檔案 (可選)')
    parser.add_argument('--mode', '-m', type=str, default='standard',
                       choices=['minimal', 'standard', 'comprehensive', 'custom'],
                       help='檢測模式: minimal(3點), standard(11點), comprehensive(20點), custom(自定義)')

    args = parser.parse_args()

    try:
        print("🚀 開始出力檢測分析...")
        print("="*60)

        # 創建分析器
        analyzer = ForceDetectionAnalyzer(detection_mode=args.mode)

        print(f"🎯 使用檢測模式: {args.mode}")
        print(f"📍 選擇的關鍵點: {list(analyzer.key_landmarks.keys())}")
        print(f"📊 關鍵點數量: {len(analyzer.key_landmarks)}")
        print("="*60)

        # 載入數據
        df = analyzer.load_pose_data(args.input)

        # 提取座標
        coordinates = analyzer.extract_coordinates(df)

        if not coordinates:
            print("❌ 沒有找到任何有效的關鍵點座標，無法進行分析")
            return

        print("="*60)

        # 計算特徵
        angles = analyzer.calculate_joint_angles(coordinates)
        motion_features = analyzer.calculate_motion_features(coordinates)
        stability_features = analyzer.calculate_stability_features(coordinates)

        # 儲存特徵到分析器
        analyzer.angles = angles
        analyzer.motion_features = motion_features
        analyzer.stability_features = stability_features

        print("="*60)

        # 檢測出力
        force_labels = analyzer.detect_force_application(
            df, coordinates, angles, motion_features, stability_features
        )

        print("="*60)

        # 訓練分類器
        if analyzer.train_force_classifier(force_labels):
            # 預測
            predictions, probabilities = analyzer.predict_force_application()

            # 視覺化
            analyzer.visualize_force_detection(df, force_labels, predictions)

            # 保存結果
            if args.output:
                df_result = df.copy()
                df_result['規則檢測_出力'] = force_labels
                df_result['ML預測_出力'] = predictions
                df_result['出力機率'] = probabilities

                df_result.to_csv(args.output, index=False, encoding='utf-8-sig')
                print(f"✅ 結果已保存到: {args.output}")

            # 顯示統計結果
            print("\n📊 出力檢測統計:")
            print(f"  規則檢測出力幀數: {np.sum(force_labels)} / {len(force_labels)} ({np.sum(force_labels)/len(force_labels)*100:.1f}%)")
            print(f"  ML預測出力幀數: {np.sum(predictions)} / {len(predictions)} ({np.sum(predictions)/len(predictions)*100:.1f}%)")
            print(f"  平均出力機率: {np.mean(probabilities):.3f}")

        print("\n🎉 出力檢測分析完成！")

    except Exception as e:
        print(f"\n❌ 分析過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        print("\n💡 請檢查輸入檔案格式和路徑是否正確")

if __name__ == "__main__":
    main()
