#!/usr/bin/env python3
"""
智能作業監控系統 - 主程式
結合影片播放、姿勢檢測、動作識別和LSTM學習的完整監控系統
"""

import cv2
import numpy as np
import pandas as pd
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import queue
import time
from datetime import datetime
import os
import sys

# MediaPipe導入處理
try:
    import mediapipe as mp
    MP_AVAILABLE = True
    print("✅ MediaPipe載入成功")
except ImportError as e:
    print(f"⚠️ MediaPipe載入失敗: {e}")
    print("將使用模擬模式運行")
    MP_AVAILABLE = False

# 添加父目錄到路徑以導入其他模組
sys.path.append('..')

try:
    import tensorflow as tf
    from tensorflow.keras.models import Sequential, load_model
    from tensorflow.keras.layers import LSTM, Dense, Dropout
    TF_AVAILABLE = True
except ImportError:
    print("⚠️ TensorFlow未安裝，將跳過LSTM功能")
    TF_AVAILABLE = False

class VideoActionMonitor:
    """智能作業監控系統主類"""
    
    def __init__(self):
        # 初始化MediaPipe
        if MP_AVAILABLE:
            self.mp_pose = mp.solutions.pose
            self.mp_drawing = mp.solutions.drawing_utils
            self.pose = self.mp_pose.Pose(
                static_image_mode=False,
                model_complexity=1,
                enable_segmentation=False,
                min_detection_confidence=0.5,
                min_tracking_confidence=0.5
            )
        else:
            self.mp_pose = None
            self.mp_drawing = None
            self.pose = None
            print("⚠️ MediaPipe不可用，將使用模擬模式")
        
        # 系統狀態
        self.video_path = None
        self.csv_data = None
        self.current_frame = 0
        self.total_frames = 0
        self.fps = 30
        self.is_playing = False
        self.is_paused = False
        
        # LSTM模型
        self.action_model = None
        self.screw_model = None
        self.label_encoder = None
        self.scaler = None
        
        # 動作識別
        self.action_history = []
        self.current_action = "待機中"
        self.screw_progress = 0
        self.total_screws = 16
        self.completed_screws = set()
        
        # 特徵緩衝區
        self.feature_buffer = []
        self.buffer_size = 30  # LSTM序列長度
        
        # 螺絲位置定義
        self.screw_actions = [
            "鎖第1顆螺絲", "鎖第2顆螺絲", "鎖第3顆螺絲", "鎖第4顆螺絲",
            "鎖第5顆螺絲", "鎖第6顆螺絲", "鎖第7顆螺絲", "鎖第8顆螺絲",
            "鎖第9顆螺絲", "鎖第10顆螺絲", "鎖第11顆螺絲", "鎖第12顆螺絲",
            "鎖第13顆螺絲", "鎖第14顆螺絲", "鎖第15顆螺絲", "鎖第16顆螺絲"
        ]
        
        # 創建GUI
        self.setup_gui()

        # 自動載入訓練好的模型
        self.auto_load_trained_model()
        
    def setup_gui(self):
        """設置圖形用戶界面"""
        self.root = tk.Tk()
        self.root.title("智能作業監控系統")
        self.root.geometry("1400x800")
        
        # 創建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左側：影片播放區域
        left_frame = ttk.LabelFrame(main_frame, text="影片播放", padding=10)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 影片顯示畫布
        self.video_canvas = tk.Canvas(left_frame, bg='black', width=640, height=480)
        self.video_canvas.pack(pady=5)
        
        # 控制按鈕
        control_frame = ttk.Frame(left_frame)
        control_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(control_frame, text="載入影片", command=self.load_video).pack(side=tk.LEFT, padx=2)
        ttk.Button(control_frame, text="載入CSV", command=self.load_csv).pack(side=tk.LEFT, padx=2)
        ttk.Button(control_frame, text="播放", command=self.play_video).pack(side=tk.LEFT, padx=2)
        ttk.Button(control_frame, text="暫停", command=self.pause_video).pack(side=tk.LEFT, padx=2)
        ttk.Button(control_frame, text="停止", command=self.stop_video).pack(side=tk.LEFT, padx=2)
        
        # 進度條
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Scale(left_frame, from_=0, to=100, orient=tk.HORIZONTAL, 
                                     variable=self.progress_var, command=self.seek_video)
        self.progress_bar.pack(fill=tk.X, pady=5)
        
        # 右側：動作分析區域
        right_frame = ttk.LabelFrame(main_frame, text="動作分析", padding=10)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        
        # 當前狀態顯示
        status_frame = ttk.LabelFrame(right_frame, text="當前狀態", padding=10)
        status_frame.pack(fill=tk.X, pady=5)
        
        self.current_action_var = tk.StringVar(value="待機中")
        ttk.Label(status_frame, text="當前動作:").pack(anchor=tk.W)
        ttk.Label(status_frame, textvariable=self.current_action_var, 
                 font=("Arial", 12, "bold"), foreground="blue").pack(anchor=tk.W)
        
        # 進度顯示
        progress_frame = ttk.LabelFrame(right_frame, text="作業進度", padding=10)
        progress_frame.pack(fill=tk.X, pady=5)
        
        self.screw_progress_var = tk.StringVar(value="0/16")
        ttk.Label(progress_frame, text="螺絲進度:").pack(anchor=tk.W)
        ttk.Label(progress_frame, textvariable=self.screw_progress_var, 
                 font=("Arial", 12, "bold"), foreground="green").pack(anchor=tk.W)
        
        # 完成螺絲列表
        completed_frame = ttk.LabelFrame(right_frame, text="已完成螺絲", padding=10)
        completed_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.completed_listbox = tk.Listbox(completed_frame, height=8)
        self.completed_listbox.pack(fill=tk.BOTH, expand=True)
        
        # 統計信息
        stats_frame = ttk.LabelFrame(right_frame, text="統計信息", padding=10)
        stats_frame.pack(fill=tk.X, pady=5)
        
        self.stats_text = tk.Text(stats_frame, height=6, width=30)
        self.stats_text.pack(fill=tk.BOTH, expand=True)
        
        # LSTM訓練按鈕
        model_frame = ttk.LabelFrame(right_frame, text="模型管理", padding=10)
        model_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(model_frame, text="訓練LSTM模型", command=self.train_lstm_model).pack(fill=tk.X, pady=2)
        ttk.Button(model_frame, text="載入模型", command=self.load_model).pack(fill=tk.X, pady=2)
        ttk.Button(model_frame, text="保存模型", command=self.save_model).pack(fill=tk.X, pady=2)

        # 模型狀態顯示
        self.model_status_var = tk.StringVar(value="模型未載入")
        ttk.Label(model_frame, textvariable=self.model_status_var,
                 font=("Arial", 9), foreground="red").pack(fill=tk.X, pady=2)
        
    def load_video(self):
        """載入影片檔案"""
        file_path = filedialog.askopenfilename(
            title="選擇影片檔案",
            filetypes=[("影片檔案", "*.mp4 *.avi *.mov *.mkv"), ("所有檔案", "*.*")]
        )
        
        if file_path:
            self.video_path = file_path
            self.cap = cv2.VideoCapture(file_path)
            self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            self.fps = self.cap.get(cv2.CAP_PROP_FPS)
            
            messagebox.showinfo("成功", f"影片載入成功！\n總幀數: {self.total_frames}\nFPS: {self.fps:.2f}")
            
    def load_csv(self):
        """載入CSV數據檔案"""
        file_path = filedialog.askopenfilename(
            title="選擇CSV數據檔案",
            filetypes=[("CSV檔案", "*.csv"), ("所有檔案", "*.*")]
        )
        
        if file_path:
            try:
                self.csv_data = pd.read_csv(file_path, encoding='utf-8-sig')
                messagebox.showinfo("成功", f"CSV數據載入成功！\n數據行數: {len(self.csv_data)}")
            except Exception as e:
                messagebox.showerror("錯誤", f"CSV載入失敗: {e}")
    
    def extract_pose_features(self, landmarks):
        """從姿勢關鍵點提取特徵"""
        if not landmarks or not landmarks.landmark:
            return None
        
        # 提取關鍵關節點
        key_points = [11, 12, 13, 14, 15, 16, 19, 20]  # 肩膀、手肘、手腕、食指
        features = []
        
        for idx in key_points:
            if idx < len(landmarks.landmark):
                landmark = landmarks.landmark[idx]
                features.extend([landmark.x, landmark.y, landmark.z])
            else:
                features.extend([0.0, 0.0, 0.0])
        
        return np.array(features)
    
    def predict_action(self, features):
        """使用LSTM模型預測動作"""
        if self.action_model is None or len(self.feature_buffer) < self.buffer_size:
            return "分析中..."

        try:
            # 準備序列數據
            sequence = np.array(self.feature_buffer[-self.buffer_size:])
            sequence = sequence.reshape(1, self.buffer_size, -1)

            # 預測
            prediction = self.action_model.predict(sequence, verbose=0)
            action_idx = np.argmax(prediction[0])
            confidence = np.max(prediction[0])

            # 使用載入的標籤編碼器
            if self.label_encoder is not None:
                action = self.label_encoder.inverse_transform([action_idx])[0]
                return f"{action} ({confidence:.2f})"
            else:
                # 備用標籤映射
                all_actions = ["尋位", "瑕疵(掉螺絲)", "旋轉工件(順時針)", "結束動作"] + self.screw_actions

                if action_idx < len(all_actions):
                    return f"{all_actions[action_idx]} ({confidence:.2f})"
                else:
                    return "未知動作"

        except Exception as e:
            print(f"動作預測錯誤: {e}")
            return "預測錯誤"
    
    def update_display(self, frame, action, screw_num=None):
        """更新顯示內容"""
        # 更新當前動作
        self.current_action_var.set(action)
        
        # 更新螺絲進度
        if screw_num and screw_num not in self.completed_screws:
            self.completed_screws.add(screw_num)
            self.completed_listbox.insert(tk.END, f"螺絲 #{screw_num}")
        
        self.screw_progress_var.set(f"{len(self.completed_screws)}/{self.total_screws}")
        
        # 更新統計信息
        stats_text = f"當前幀: {self.current_frame}\n"
        stats_text += f"總幀數: {self.total_frames}\n"
        stats_text += f"播放時間: {self.current_frame/self.fps:.1f}s\n"
        stats_text += f"完成率: {len(self.completed_screws)/self.total_screws*100:.1f}%\n"
        
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats_text)
        
        # 顯示影片幀
        self.display_frame(frame)
    
    def display_frame(self, frame):
        """在畫布上顯示影片幀"""
        if frame is not None:
            # 調整幀大小
            height, width = frame.shape[:2]
            canvas_width = self.video_canvas.winfo_width()
            canvas_height = self.video_canvas.winfo_height()
            
            if canvas_width > 1 and canvas_height > 1:
                # 計算縮放比例
                scale = min(canvas_width/width, canvas_height/height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                
                # 調整大小
                frame_resized = cv2.resize(frame, (new_width, new_height))
                
                # 轉換為RGB
                frame_rgb = cv2.cvtColor(frame_resized, cv2.COLOR_BGR2RGB)
                
                # 轉換為PhotoImage
                from PIL import Image, ImageTk
                image = Image.fromarray(frame_rgb)
                photo = ImageTk.PhotoImage(image)
                
                # 顯示在畫布上
                self.video_canvas.delete("all")
                self.video_canvas.create_image(canvas_width//2, canvas_height//2, image=photo, anchor=tk.CENTER)
                self.video_canvas.image = photo  # 保持引用
    
    def play_video(self):
        """播放影片"""
        if self.video_path and self.csv_data is not None:
            self.is_playing = True
            self.is_paused = False
            threading.Thread(target=self.video_playback_thread, daemon=True).start()
        else:
            messagebox.showwarning("警告", "請先載入影片和CSV數據")
    
    def pause_video(self):
        """暫停影片"""
        self.is_paused = not self.is_paused
    
    def stop_video(self):
        """停止影片"""
        self.is_playing = False
        self.current_frame = 0
        self.progress_var.set(0)
    
    def seek_video(self, value):
        """跳轉到指定位置"""
        if hasattr(self, 'cap'):
            frame_num = int(float(value) * self.total_frames / 100)
            self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
            self.current_frame = frame_num
    
    def video_playback_thread(self):
        """影片播放線程"""
        while self.is_playing and hasattr(self, 'cap'):
            if not self.is_paused:
                ret, frame = self.cap.read()
                if not ret:
                    break
                
                # 處理當前幀
                self.process_frame(frame)
                
                # 更新進度
                self.current_frame += 1
                progress = (self.current_frame / self.total_frames) * 100
                self.progress_var.set(progress)
                
                # 控制播放速度
                time.sleep(1.0 / self.fps)
            else:
                time.sleep(0.1)
    
    def process_frame(self, frame):
        """處理單個影片幀"""
        current_action = "待機中"
        screw_num = None

        if MP_AVAILABLE and self.pose is not None:
            # MediaPipe姿勢檢測
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.pose.process(frame_rgb)

            if results.pose_landmarks:
                # 繪製骨架
                self.mp_drawing.draw_landmarks(
                    frame, results.pose_landmarks, self.mp_pose.POSE_CONNECTIONS
                )

                # 提取特徵
                features = self.extract_pose_features(results.pose_landmarks)

                if features is not None:
                    self.feature_buffer.append(features)
                    if len(self.feature_buffer) > self.buffer_size:
                        self.feature_buffer.pop(0)

                    # 預測動作
                    current_action = self.predict_action(features)

                    # 檢查是否完成螺絲
                    if "鎖第" in current_action and "螺絲" in current_action:
                        try:
                            screw_num = int(current_action.split("鎖第")[1].split("顆螺絲")[0])
                        except:
                            pass
        else:
            # 模擬模式 - 使用CSV數據
            if self.csv_data is not None and self.current_frame < len(self.csv_data):
                row = self.csv_data.iloc[self.current_frame]
                current_action = row.get('標籤', '模擬模式')

                # 檢查是否完成螺絲
                if "鎖第" in current_action and "螺絲" in current_action:
                    try:
                        screw_num = int(current_action.split("鎖第")[1].split("顆螺絲")[0])
                    except:
                        pass

        # 更新顯示
        self.root.after(0, lambda: self.update_display(frame, current_action, screw_num))
    
    def train_lstm_model(self):
        """訓練LSTM模型"""
        if self.csv_data is None:
            messagebox.showwarning("警告", "請先載入CSV數據")
            return
        
        if not TF_AVAILABLE:
            messagebox.showerror("錯誤", "TensorFlow未安裝，無法訓練模型")
            return
        
        messagebox.showinfo("訓練", "開始訓練LSTM模型，請稍候...")
        threading.Thread(target=self.train_model_thread, daemon=True).start()
    
    def train_model_thread(self):
        """模型訓練線程 - 調用專業GPU訓練程式"""
        try:
            if self.csv_data is None:
                self.root.after(0, lambda: messagebox.showwarning("警告", "請先載入CSV數據"))
                return

            # 保存當前CSV數據到臨時檔案
            temp_csv = "data/temp_training_data.csv"
            self.csv_data.to_csv(temp_csv, index=False, encoding='utf-8-sig')

            # 調用專業GPU訓練程式
            import subprocess
            import sys

            cmd = [
                sys.executable,
                "gpu_lstm_trainer.py",
                "--data", temp_csv,
                "--output", "models/gui_trained_model.h5",
                "--epochs", "50",  # GUI模式使用較少輪數
                "--batch-size", "32"
            ]

            print("🚀 啟動GPU訓練程式...")
            print(f"命令: {' '.join(cmd)}")

            # 運行訓練程式
            process = subprocess.run(cmd, capture_output=True, text=True, cwd=".")

            if process.returncode == 0:
                print("✅ GPU訓練完成")
                print(process.stdout)

                # 自動載入訓練好的模型
                model_path = "models/gui_trained_model.h5"
                if os.path.exists(model_path) and TF_AVAILABLE:
                    self.action_model = load_model(model_path)

                    # 載入標籤編碼器
                    import pickle
                    if os.path.exists("models/label_encoder.pkl"):
                        with open("models/label_encoder.pkl", 'rb') as f:
                            self.label_encoder = pickle.load(f)

                    self.root.after(0, lambda: messagebox.showinfo("完成",
                        "🎉 GPU加速LSTM模型訓練完成！\n✅ 模型已自動載入到監控系統\n📊 可以開始實時動作識別"))
                else:
                    self.root.after(0, lambda: messagebox.showinfo("完成",
                        "訓練完成，但模型載入失敗"))
            else:
                print("❌ 訓練失敗")
                print("STDOUT:", process.stdout)
                print("STDERR:", process.stderr)
                self.root.after(0, lambda: messagebox.showerror("錯誤",
                    f"GPU訓練失敗:\n{process.stderr[:200]}..."))

        except Exception as e:
            print(f"訓練線程錯誤: {e}")
            self.root.after(0, lambda: messagebox.showerror("錯誤", f"模型訓練失敗: {e}"))
    
    def load_model(self):
        """載入已訓練的模型"""
        file_path = filedialog.askopenfilename(
            title="選擇模型檔案",
            filetypes=[("模型檔案", "*.h5 *.keras"), ("所有檔案", "*.*")]
        )

        if file_path and TF_AVAILABLE:
            try:
                self.action_model = load_model(file_path)

                # 嘗試載入對應的標籤編碼器
                import pickle
                model_dir = os.path.dirname(file_path)
                label_encoder_path = os.path.join(model_dir, "label_encoder.pkl")
                scaler_path = os.path.join(model_dir, "scaler.pkl")

                if os.path.exists(label_encoder_path):
                    with open(label_encoder_path, 'rb') as f:
                        self.label_encoder = pickle.load(f)
                    print("✅ 標籤編碼器載入成功")

                if os.path.exists(scaler_path):
                    with open(scaler_path, 'rb') as f:
                        self.scaler = pickle.load(f)
                    print("✅ 特徵標準化器載入成功")

                messagebox.showinfo("成功", "🎉 模型載入成功！\n✅ 已載入相關配置檔案\n🚀 可以開始實時動作識別")
            except Exception as e:
                messagebox.showerror("錯誤", f"模型載入失敗: {e}")
    
    def save_model(self):
        """保存訓練好的模型"""
        if self.action_model is None:
            messagebox.showwarning("警告", "沒有可保存的模型")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="保存模型",
            defaultextension=".h5",
            filetypes=[("模型檔案", "*.h5"), ("所有檔案", "*.*")]
        )
        
        if file_path:
            try:
                self.action_model.save(file_path)
                messagebox.showinfo("成功", "模型保存成功！")
            except Exception as e:
                messagebox.showerror("錯誤", f"模型保存失敗: {e}")
    
    def auto_load_trained_model(self):
        """自動載入訓練好的模型"""
        try:
            if os.path.exists('models/lstm_action_model.h5') and TF_AVAILABLE:
                self.action_model = load_model('models/lstm_action_model.h5')

                # 載入標籤編碼器
                import pickle
                if os.path.exists('models/label_encoder.pkl'):
                    with open('models/label_encoder.pkl', 'rb') as f:
                        self.label_encoder = pickle.load(f)

                if os.path.exists('models/scaler.pkl'):
                    with open('models/scaler.pkl', 'rb') as f:
                        self.scaler = pickle.load(f)

                self.model_status_var.set("✅ LSTM模型已載入")
                print("🤖 自動載入訓練好的LSTM模型成功")
            else:
                self.model_status_var.set("❌ 找不到訓練模型")
        except Exception as e:
            self.model_status_var.set("❌ 模型載入失敗")
            print(f"模型自動載入失敗: {e}")

    def run(self):
        """運行主程式"""
        self.root.mainloop()

def main():
    """主函數"""
    print("🚀 啟動智能作業監控系統...")
    
    # 檢查依賴
    try:
        from PIL import Image, ImageTk
    except ImportError:
        print("❌ 需要安裝 Pillow: pip install Pillow")
        return
    
    # 創建並運行監控系統
    monitor = VideoActionMonitor()
    monitor.run()

if __name__ == "__main__":
    main()
