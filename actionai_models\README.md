# ActionAI 模型

## 模型資訊
- **模型類型**: ActionAI專業動作辨識算法 (雙層LSTM)
- **準確率**: 41.67%
- **訓練數據**: 56個序列樣本
- **序列長度**: 30幀 (1秒)
- **關鍵點**: 33個MediaPipe關鍵點 (66個特徵)

## 檔案說明
- `actionai_model.h5` - Keras模型檔案
- `actionai_label_encoder.pkl` - 標籤編碼器
- `actionai_labels.txt` - 標籤列表

## 模型特點
- ✅ 基於專業ActionAI算法
- ✅ 使用L2正規化處理關鍵點
- ❌ 目前有欠擬合問題
- ❌ 100%預測為旋轉工件動作

## 預測結果 (視頻2)
- 旋轉工件(順時針): 100.0%
- 平均信心度: 0.172 (較低)

## 支援的動作類別 (13種)
1. 旋轉工件(順時針)
2. 結束動作
3. 鎖第11顆螺絲
4. 鎖第13顆螺絲
5. 鎖第14顆螺絲
6. 鎖第15顆螺絲
7. 鎖第16顆螺絲
8. 鎖第1顆螺絲
9. 鎖第4顆螺絲
10. 鎖第6顆螺絲
11. 鎖第7顆螺絲
12. 鎖第8顆螺絲
13. 鎖第9顆螺絲

## 使用方法
```python
# 訓練模型
python actionai_trainer.py

# 預測視頻2
python actionai_predict.py
```

## 改進建議
- 增加訓練數據量
- 調整正規化策略
- 優化模型架構
