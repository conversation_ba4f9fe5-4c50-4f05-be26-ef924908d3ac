
# 🤖 LSTM動作識別模型展示報告

## 📊 整體性能
- **測試樣本數**: 1,560 個
- **預測準確率**: 0.9590 (95.90%)
- **正確預測**: 1,496 個
- **錯誤預測**: 64 個

## 🎯 各動作識別準確率

| 動作類別 | 樣本數 | 正確數 | 準確率 |
|---------|--------|--------|--------|
| 尋位 | 450 | 405 | 90.00% |
| 旋轉工件(順時針) | 150 | 149 | 99.33% |
| 瑕疵(掉螺絲) | 120 | 120 | 100.00% |
| 結束動作 | 30 | 29 | 96.67% |
| 鎖第10顆螺絲 | 30 | 30 | 100.00% |
| 鎖第11顆螺絲 | 60 | 59 | 98.33% |
| 鎖第12顆螺絲 | 60 | 60 | 100.00% |
| 鎖第13顆螺絲 | 60 | 60 | 100.00% |
| 鎖第14顆螺絲 | 30 | 30 | 100.00% |
| 鎖第15顆螺絲 | 30 | 28 | 93.33% |
| 鎖第16顆螺絲 | 60 | 58 | 96.67% |
| 鎖第1顆螺絲 | 90 | 90 | 100.00% |
| 鎖第2顆螺絲 | 30 | 30 | 100.00% |
| 鎖第3顆螺絲 | 60 | 54 | 90.00% |
| 鎖第4顆螺絲 | 30 | 27 | 90.00% |
| 鎖第5顆螺絲 | 60 | 59 | 98.33% |
| 鎖第6顆螺絲 | 60 | 60 | 100.00% |
| 鎖第7顆螺絲 | 30 | 28 | 93.33% |
| 鎖第8顆螺絲 | 60 | 60 | 100.00% |
| 鎖第9顆螺絲 | 60 | 60 | 100.00% |

## 📈 性能分析

### ✅ 優秀表現 (準確率 > 90%)
- **鎖第1顆螺絲**: 100.0% (90/90)
- **鎖第2顆螺絲**: 100.0% (30/30)
- **瑕疵(掉螺絲)**: 100.0% (120/120)
- **鎖第5顆螺絲**: 98.3% (59/60)
- **鎖第6顆螺絲**: 100.0% (60/60)
- **旋轉工件(順時針)**: 99.3% (149/150)
- **鎖第7顆螺絲**: 93.3% (28/30)
- **鎖第8顆螺絲**: 100.0% (60/60)
- **鎖第9顆螺絲**: 100.0% (60/60)
- **鎖第10顆螺絲**: 100.0% (30/30)
- **鎖第11顆螺絲**: 98.3% (59/60)
- **鎖第12顆螺絲**: 100.0% (60/60)
- **鎖第13顆螺絲**: 100.0% (60/60)
- **鎖第14顆螺絲**: 100.0% (30/30)
- **鎖第15顆螺絲**: 93.3% (28/30)
- **鎖第16顆螺絲**: 96.7% (58/60)
- **結束動作**: 96.7% (29/30)

### 🟡 良好表現 (準確率 70-90%)
- **尋位**: 90.0% (405/450)
- **鎖第3顆螺絲**: 90.0% (54/60)
- **鎖第4顆螺絲**: 90.0% (27/30)

### 🔴 需要改進 (準確率 < 70%)
- 🎉 所有動作都達到良好以上的識別準確率！

## 🚀 模型特點

### 技術規格
- **模型類型**: 雙向LSTM + 批次正規化
- **序列長度**: 30幀 (約1秒)
- **特徵維度**: 24維 (8個關鍵點 × 3座標)
- **動作類別**: 20 種

### 實際應用
- **實時監控**: 可用於即時作業監控
- **品質檢測**: 自動識別操作異常
- **培訓輔助**: 協助新員工學習標準動作
- **效率分析**: 統計作業時間和效率

## 📋 詳細預測結果 (前20個)

| 幀數 | 時間 | 真實動作 | 預測動作 | 信心度 | 正確性 |
|------|------|----------|----------|--------|--------|
| 29 | 00:00.97 | 尋位 | 鎖第2顆螺絲 | 0.502 | ❌ |
| 30 | 00:01.00 | 鎖第1顆螺絲 | 鎖第1顆螺絲 | 0.384 | ✅ |
| 31 | 00:01.03 | 鎖第1顆螺絲 | 鎖第1顆螺絲 | 0.534 | ✅ |
| 32 | 00:01.07 | 鎖第1顆螺絲 | 鎖第1顆螺絲 | 0.698 | ✅ |
| 33 | 00:01.10 | 鎖第1顆螺絲 | 鎖第1顆螺絲 | 0.836 | ✅ |
| 34 | 00:01.13 | 鎖第1顆螺絲 | 鎖第1顆螺絲 | 0.915 | ✅ |
| 35 | 00:01.17 | 鎖第1顆螺絲 | 鎖第1顆螺絲 | 0.950 | ✅ |
| 36 | 00:01.20 | 鎖第1顆螺絲 | 鎖第1顆螺絲 | 0.968 | ✅ |
| 37 | 00:01.23 | 鎖第1顆螺絲 | 鎖第1顆螺絲 | 0.980 | ✅ |
| 38 | 00:01.27 | 鎖第1顆螺絲 | 鎖第1顆螺絲 | 0.988 | ✅ |
| 39 | 00:01.30 | 鎖第1顆螺絲 | 鎖第1顆螺絲 | 0.991 | ✅ |
| 40 | 00:01.33 | 鎖第1顆螺絲 | 鎖第1顆螺絲 | 0.993 | ✅ |
| 41 | 00:01.37 | 鎖第1顆螺絲 | 鎖第1顆螺絲 | 0.995 | ✅ |
| 42 | 00:01.40 | 鎖第1顆螺絲 | 鎖第1顆螺絲 | 0.997 | ✅ |
| 43 | 00:01.43 | 鎖第1顆螺絲 | 鎖第1顆螺絲 | 0.997 | ✅ |
| 44 | 00:01.47 | 鎖第1顆螺絲 | 鎖第1顆螺絲 | 0.998 | ✅ |
| 45 | 00:01.50 | 鎖第1顆螺絲 | 鎖第1顆螺絲 | 0.998 | ✅ |
| 46 | 00:01.53 | 鎖第1顆螺絲 | 鎖第1顆螺絲 | 0.999 | ✅ |
| 47 | 00:01.57 | 鎖第1顆螺絲 | 鎖第1顆螺絲 | 0.999 | ✅ |
| 48 | 00:01.60 | 鎖第1顆螺絲 | 鎖第1顆螺絲 | 0.999 | ✅ |

... 還有 1540 個預測結果

---

**報告生成時間**: 2025-07-15 14:33:35
**模型檔案**: models/lstm_action_model.h5
**測試數據**: data/1_pose_data_繁體中文_清理版_含時間_含標籤.csv
