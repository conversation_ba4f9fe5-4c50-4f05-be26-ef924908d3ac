# 時間軸螺絲孔位置配置 - 20250717_100655
# 由時間軸螺絲孔標記工具自動生成

def define_screw_positions(self):
    """定義16個螺絲孔位置 - 時間軸標記版本"""
    print('📍 載入時間軸標記的16個螺絲孔位置...')
    
    positions = {
        1: {
            'x_min': 1275,
            'x_max': 1323,
            'y_min': 593,
            'y_max': 644,
            'center': (1299, 618),
            'marked_at_time': '00:03',
            'marked_at_frame': 108
        },
        2: {
            'x_min': 1418,
            'x_max': 1464,
            'y_min': 528,
            'y_max': 570,
            'center': (1441, 549),
            'marked_at_time': '00:06',
            'marked_at_frame': 188
        },
        3: {
            'x_min': 1397,
            'x_max': 1453,
            'y_min': 537,
            'y_max': 578,
            'center': (1425, 557),
            'marked_at_time': '00:08',
            'marked_at_frame': 249
        },
        4: {
            'x_min': 1346,
            'x_max': 1406,
            'y_min': 554,
            'y_max': 583,
            'center': (1376, 568),
            'marked_at_time': '00:10',
            'marked_at_frame': 310
        },
        5: {
            'x_min': 1430,
            'x_max': 1498,
            'y_min': 528,
            'y_max': 572,
            'center': (1464, 550),
            'marked_at_time': '00:12',
            'marked_at_frame': 368
        },
        6: {
            'x_min': 1445,
            'x_max': 1502,
            'y_min': 535,
            'y_max': 578,
            'center': (1473, 556),
            'marked_at_time': '00:14',
            'marked_at_frame': 436
        },
        7: {
            'x_min': 1269,
            'x_max': 1349,
            'y_min': 612,
            'y_max': 663,
            'center': (1309, 637),
            'marked_at_time': '00:21',
            'marked_at_frame': 635
        },
        8: {
            'x_min': 1190,
            'x_max': 1261,
            'y_min': 663,
            'y_max': 705,
            'center': (1225, 684),
            'marked_at_time': '00:24',
            'marked_at_frame': 722
        },
        9: {
            'x_min': 1115,
            'x_max': 1181,
            'y_min': 643,
            'y_max': 698,
            'center': (1148, 670),
            'marked_at_time': '00:26',
            'marked_at_frame': 780
        },
        10: {
            'x_min': 1002,
            'x_max': 1050,
            'y_min': 676,
            'y_max': 730,
            'center': (1026, 703),
            'marked_at_time': '00:27',
            'marked_at_frame': 838
        },
        11: {
            'x_min': 1114,
            'x_max': 1208,
            'y_min': 521,
            'y_max': 564,
            'center': (1161, 542),
            'marked_at_time': '00:31',
            'marked_at_frame': 936
        },
        12: {
            'x_min': 890,
            'x_max': 978,
            'y_min': 630,
            'y_max': 680,
            'center': (934, 655),
            'marked_at_time': '00:33',
            'marked_at_frame': 1008
        },
        13: {
            'x_min': 1059,
            'x_max': 1118,
            'y_min': 544,
            'y_max': 590,
            'center': (1088, 567),
            'marked_at_time': '00:35',
            'marked_at_frame': 1076
        },
        14: {
            'x_min': 1026,
            'x_max': 1090,
            'y_min': 583,
            'y_max': 621,
            'center': (1058, 602),
            'marked_at_time': '00:38',
            'marked_at_frame': 1163
        },
        15: {
            'x_min': 1014,
            'x_max': 1088,
            'y_min': 589,
            'y_max': 637,
            'center': (1051, 613),
            'marked_at_time': '00:42',
            'marked_at_frame': 1265
        },
        16: {
            'x_min': 1078,
            'x_max': 1158,
            'y_min': 562,
            'y_max': 621,
            'center': (1118, 591),
            'marked_at_time': '00:45',
            'marked_at_frame': 1363
        },
    }}
    
    self.screw_positions = positions
    print(f'✅ 載入了 {{len(positions)}} 個時間軸標記的螺絲孔位置')
    
    # 顯示標記時間資訊
    print('📍 螺絲孔標記時間:')
    for i in range(1, 17):
        if i in positions:
            pos = positions[i]
            print(f'  螺絲孔{i:2d}: {pos["marked_at_time"]} 中心({pos["center"][0]}, {pos["center"][1]})')
