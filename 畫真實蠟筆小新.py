#!/usr/bin/env python3
"""
根據官方圖片畫更像的蠟筆小新
參考官方S.H.Figuarts設計
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np

def draw_realistic_shin_chan():
    """畫更像的蠟筆小新"""
    
    # 創建畫布
    fig, ax = plt.subplots(1, 1, figsize=(8, 10))
    ax.set_xlim(0, 8)
    ax.set_ylim(0, 10)
    ax.set_aspect('equal')
    ax.axis('off')
    
    # 設置背景色
    fig.patch.set_facecolor('lightcyan')
    
    # 1. 畫頭部 (更大的橢圓，符合小新的大頭特徵)
    head = patches.Ellipse((4, 7.8), 3.2, 3.8, facecolor='#FFDBAC', edgecolor='black', linewidth=2.5)
    ax.add_patch(head)
    
    # 2. 畫特色眉毛 (更粗更黑，小新的招牌特徵)
    # 左眉毛 - 更粗更彎
    eyebrow_left_x = np.array([2.8, 3.2, 3.6])
    eyebrow_left_y = np.array([8.5, 8.7, 8.5])
    ax.plot(eyebrow_left_x, eyebrow_left_y, 'k-', linewidth=10, solid_capstyle='round')
    
    # 右眉毛 - 更粗更彎
    eyebrow_right_x = np.array([4.4, 4.8, 5.2])
    eyebrow_right_y = np.array([8.5, 8.7, 8.5])
    ax.plot(eyebrow_right_x, eyebrow_right_y, 'k-', linewidth=10, solid_capstyle='round')
    
    # 3. 畫眼睛 (小新的特色小眼睛)
    # 左眼 - 更小更圓
    left_eye = patches.Circle((3.4, 8), 0.25, facecolor='white', edgecolor='black', linewidth=2)
    ax.add_patch(left_eye)
    # 左眼珠 - 很小的黑點
    left_pupil = patches.Circle((3.4, 8), 0.08, facecolor='black')
    ax.add_patch(left_pupil)
    
    # 右眼 - 更小更圓
    right_eye = patches.Circle((4.6, 8), 0.25, facecolor='white', edgecolor='black', linewidth=2)
    ax.add_patch(right_eye)
    # 右眼珠 - 很小的黑點
    right_pupil = patches.Circle((4.6, 8), 0.08, facecolor='black')
    ax.add_patch(right_pupil)
    
    # 4. 畫鼻子 (更小的點)
    nose = patches.Circle((4, 7.4), 0.06, facecolor='black')
    ax.add_patch(nose)
    
    # 5. 畫嘴巴 (小新特色的小嘴)
    # 更小的弧形嘴巴
    theta = np.linspace(0.3*np.pi, 0.7*np.pi, 20)
    mouth_x = 4 + 0.3 * np.cos(theta)
    mouth_y = 6.9 + 0.15 * np.sin(theta)
    ax.plot(mouth_x, mouth_y, 'k-', linewidth=3, solid_capstyle='round')
    
    # 6. 畫頭髮 (小新的特色髮型 - 只有幾根毛)
    # 中間最高的頭髮
    ax.plot([4, 4], [9.6, 10.2], 'k-', linewidth=5, solid_capstyle='round')
    # 左邊的頭髮
    ax.plot([3.6, 3.4], [9.4, 9.8], 'k-', linewidth=4, solid_capstyle='round')
    # 右邊的頭髮
    ax.plot([4.4, 4.6], [9.4, 9.8], 'k-', linewidth=4, solid_capstyle='round')
    
    # 7. 畫身體 (紅色上衣，更符合比例)
    body = patches.Rectangle((3.1, 4.2), 1.8, 2.8, facecolor='#FF4444', edgecolor='black', linewidth=2.5)
    ax.add_patch(body)
    
    # 8. 畫手臂 (更短更粗，符合小新的比例)
    # 左手臂
    left_arm = patches.Rectangle((2.2, 5.8), 0.9, 0.7, facecolor='#FFDBAC', edgecolor='black', linewidth=2)
    ax.add_patch(left_arm)
    # 右手臂
    right_arm = patches.Rectangle((4.9, 5.8), 0.9, 0.7, facecolor='#FFDBAC', edgecolor='black', linewidth=2)
    ax.add_patch(right_arm)
    
    # 9. 畫手 (圓形手掌)
    # 左手
    left_hand = patches.Circle((2.2, 6.15), 0.35, facecolor='#FFDBAC', edgecolor='black', linewidth=2)
    ax.add_patch(left_hand)
    # 右手
    right_hand = patches.Circle((5.8, 6.15), 0.35, facecolor='#FFDBAC', edgecolor='black', linewidth=2)
    ax.add_patch(right_hand)
    
    # 10. 畫褲子 (黃色短褲)
    pants = patches.Rectangle((3.1, 2.2), 1.8, 2, facecolor='#FFD700', edgecolor='black', linewidth=2.5)
    ax.add_patch(pants)
    
    # 11. 畫腿 (更短更粗)
    # 左腿
    left_leg = patches.Rectangle((3.2, 0.8), 0.6, 1.4, facecolor='#FFDBAC', edgecolor='black', linewidth=2)
    ax.add_patch(left_leg)
    # 右腿
    right_leg = patches.Rectangle((4.2, 0.8), 0.6, 1.4, facecolor='#FFDBAC', edgecolor='black', linewidth=2)
    ax.add_patch(right_leg)
    
    # 12. 畫鞋子 (更大的鞋子)
    # 左鞋
    left_shoe = patches.Ellipse((3.5, 0.5), 1.2, 0.6, facecolor='#8B4513', edgecolor='black', linewidth=2)
    ax.add_patch(left_shoe)
    # 右鞋
    right_shoe = patches.Ellipse((4.5, 0.5), 1.2, 0.6, facecolor='#8B4513', edgecolor='black', linewidth=2)
    ax.add_patch(right_shoe)
    
    # 13. 添加臉部細節
    # 臉頰紅暈
    left_cheek = patches.Circle((3.2, 7.2), 0.2, facecolor='#FFB6C1', alpha=0.6)
    ax.add_patch(left_cheek)
    right_cheek = patches.Circle((4.8, 7.2), 0.2, facecolor='#FFB6C1', alpha=0.6)
    ax.add_patch(right_cheek)
    
    # 14. 在上衣上畫小圖案
    shirt_button = patches.Circle((4, 5.5), 0.12, facecolor='white', edgecolor='black', linewidth=1)
    ax.add_patch(shirt_button)
    
    # 15. 添加標題
    plt.title('Crayon Shin-chan (Official Style)', fontsize=18, fontweight='bold', pad=20)
    
    # 16. 添加一些可愛的裝飾
    # 畫一些小星星
    star_positions = [(1, 9), (7, 9), (1.5, 7.5), (6.5, 7.5), (1, 5), (7, 5)]
    for x, y in star_positions:
        # 畫五角星
        star_angles = np.linspace(0, 2*np.pi, 6)
        star_x = x + 0.15 * np.cos(star_angles - np.pi/2)
        star_y = y + 0.15 * np.sin(star_angles - np.pi/2)
        ax.plot(star_x, star_y, 'gold', marker='*', markersize=12, markeredgecolor='orange', markeredgewidth=1)
    
    # 17. 添加小新的特色表情線
    # 在眼睛下方畫小線條
    ax.plot([3.2, 3.6], [7.7, 7.7], 'k-', linewidth=1, alpha=0.5)
    ax.plot([4.4, 4.8], [7.7, 7.7], 'k-', linewidth=1, alpha=0.5)
    
    # 直接保存圖片
    plt.savefig('真實蠟筆小新.png', dpi=300, bbox_inches='tight', facecolor='lightcyan')
    print('🎨 更像的蠟筆小新畫好了！已保存為 真實蠟筆小新.png')
    
    # 關閉圖形
    plt.close()

if __name__ == "__main__":
    print('🎨 開始畫更像的蠟筆小新...')
    print('📖 參考官方S.H.Figuarts設計')
    draw_realistic_shin_chan()
    print('🎉 完成！更符合官方設計的蠟筆小新！')
