# 🤖 智能作業監控系統

這是一個完整的智能作業監控系統，結合影片播放、姿勢檢測、動作識別和LSTM學習，實現實時的螺絲鎖緊作業監控。

## 📁 資料夾結構

```
intelligent_work_monitor/
├── video_action_monitor.py    # 主程式 - 雙視窗監控系統
├── screw_monitor.py          # 螺絲監控核心邏輯
├── new_pytorch_trainer.py    # PyTorch LSTM訓練器
├── models/                   # 訓練好的模型檔案
├── data/                     # 訓練數據和CSV檔案
│   └── 1_pose_data_繁體中文_清理版_含時間_含標籤.csv
├── config/                   # 配置檔案
│   ├── test_pose_config.json
│   ├── task_area_config.json
│   └── sop_config.json
├── results/                  # 分析結果和報告
└── docs/                     # 文檔和說明
```

## 🚀 主要功能

### 🎬 雙視窗顯示系統
- **主視窗**: 播放原始影片 + MediaPipe骨架顯示
- **分析視窗**: 實時動作識別結果和統計信息

### 🔧 核心功能
1. **影片播放控制** - 播放、暫停、停止、進度跳轉
2. **實時姿勢檢測** - MediaPipe 33個關鍵點提取
3. **動作識別** - LSTM模型識別19種SOP動作
4. **螺絲進度追蹤** - 16顆螺絲完成狀態監控
5. **統計分析** - 實時統計和效率分析

### 🎯 監控內容
- **當前動作**: "鎖第5顆螺絲" / "旋轉工件" / "尋位"
- **完成進度**: "5/16顆螺絲完成"
- **工作狀態**: "approaching/engaging/rotating/complete"
- **統計信息**: 播放時間、完成率、效率分析

## 🛠️ 安裝需求

### 必需依賴
```bash
pip install opencv-python
pip install mediapipe
pip install pandas
pip install numpy
pip install tkinter
pip install Pillow
```

### 可選依賴 (LSTM功能)
```bash
pip install tensorflow
pip install scikit-learn
pip install matplotlib
```

## 🚀 使用方法

### 1. 啟動系統
```bash
cd intelligent_work_monitor
python video_action_monitor.py
```

### 2. 載入檔案
1. 點擊 "載入影片" - 選擇要分析的影片檔案
2. 點擊 "載入CSV" - 選擇對應的姿勢數據CSV檔案

### 3. 訓練模型 (首次使用)
1. 點擊 "訓練LSTM模型" - 使用CSV標籤數據訓練
2. 等待訓練完成
3. 點擊 "保存模型" - 保存訓練好的模型

### 4. 開始監控
1. 點擊 "播放" - 開始影片播放和動作分析
2. 觀察右側分析視窗的實時結果
3. 使用進度條跳轉到特定時間點

## 📊 顯示界面說明

### 左側 - 影片播放區域
- **影片畫面**: 顯示原始影片 + MediaPipe骨架
- **控制按鈕**: 播放、暫停、停止控制
- **進度條**: 顯示播放進度，可拖拽跳轉

### 右側 - 動作分析區域

#### 當前狀態
- **當前動作**: 顯示正在進行的動作
- **動作信心度**: 顯示識別的可信度

#### 作業進度
- **螺絲進度**: "5/16" 格式顯示完成進度
- **完成百分比**: 整體作業完成率

#### 已完成螺絲
- **列表顯示**: 已完成的螺絲編號
- **時間記錄**: 每顆螺絲的完成時間

#### 統計信息
- **當前幀數**: 影片播放位置
- **播放時間**: 當前播放時間
- **完成率**: 作業完成百分比
- **效率分析**: 平均每顆螺絲用時

#### 模型管理
- **訓練模型**: 使用當前CSV數據訓練LSTM
- **載入模型**: 載入已訓練的模型檔案
- **保存模型**: 保存當前訓練的模型

## 🎯 技術特點

### 🔧 系統架構
```
影片幀 → MediaPipe → 關鍵點提取 → 特徵工程 → LSTM模型 → 動作識別
   ↓                                                        ↓
骨架顯示                                                實時結果顯示
```

### 🧠 LSTM模型
- **輸入**: 30幀序列的姿勢特徵 (8個關鍵點 × 3座標)
- **輸出**: 19種動作分類 (16顆螺絲 + 3種其他動作)
- **架構**: 雙向LSTM + 注意力機制

### 📊 特徵提取
- **關鍵點**: 肩膀、手肘、手腕、食指 (8個點)
- **特徵**: XYZ座標、角度、速度、穩定性
- **序列**: 30幀滑動窗口

## 🎯 應用場景

### 👨‍🏭 工業應用
- **新員工培訓** - 標準動作學習和練習
- **品質監控** - 實時檢查作業品質
- **效率分析** - 統計作業時間和效率
- **異常檢測** - 及時發現操作問題

### 📈 管理應用
- **績效評估** - 客觀的作業評估
- **流程優化** - 識別改進機會
- **標準化** - 建立標準作業程序
- **培訓評估** - 培訓效果量化

## 🔧 自定義設置

### 修改螺絲數量
在 `video_action_monitor.py` 中修改:
```python
self.total_screws = 16  # 改為您的螺絲數量
```

### 調整LSTM參數
在 `new_pytorch_trainer.py` 中修改:
```python
sequence_length = 30    # 序列長度
hidden_size = 128      # 隱藏層大小
num_layers = 2         # LSTM層數
```

### 修改檢測閾值
在 `screw_monitor.py` 中修改:
```python
similarity_threshold = 0.7  # 螺絲位置相似度閾值
```

## 📝 注意事項

1. **首次使用需要訓練模型** - 使用您的標籤數據
2. **影片和CSV需要對應** - 確保幀數匹配
3. **需要足夠的訓練數據** - 每種動作至少100個樣本
4. **GPU加速** - 建議使用GPU進行LSTM訓練

## 🆘 故障排除

### 常見問題
1. **模型訓練失敗** - 檢查TensorFlow安裝
2. **影片無法播放** - 檢查OpenCV和影片格式
3. **CSV載入錯誤** - 檢查檔案編碼和格式
4. **動作識別不準** - 需要更多訓練數據或調整參數

### 性能優化
1. **降低影片解析度** - 提高處理速度
2. **調整序列長度** - 平衡準確度和速度
3. **使用GPU** - 加速LSTM推理

---

**這個系統提供了完整的智能作業監控解決方案，可以根據具體需求進行客製化調整！** 🚀
