#!/usr/bin/env python3
"""
全新的PyTorch LSTM訓練器
基於100%成功率的姿勢數據重新建立
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import pandas as pd
import numpy as np
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.model_selection import train_test_split
import pickle
import os
from datetime import datetime

class SOPDataset(Dataset):
    """SOP動作數據集"""
    
    def __init__(self, sequences, labels):
        self.sequences = torch.FloatTensor(sequences)
        self.labels = torch.LongTensor(labels)
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        return self.sequences[idx], self.labels[idx]

class ImprovedSOPLSTM(nn.Module):
    """改進的SOP LSTM模型"""
    
    def __init__(self, input_size, hidden_size=128, num_layers=2, num_classes=19, dropout=0.3):
        super(ImprovedSOPLSTM, self).__init__()
        
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # LSTM層
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=True
        )
        
        # 注意力機制
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_size * 2,  # 雙向LSTM
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )
        
        # 分類器
        self.classifier = nn.Sequential(
            nn.Linear(hidden_size * 2, hidden_size),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 2, num_classes)
        )
        
    def forward(self, x):
        # LSTM
        lstm_out, _ = self.lstm(x)
        
        # 注意力機制
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)
        
        # 取最後一個時間步
        final_out = attn_out[:, -1, :]
        
        # 分類
        output = self.classifier(final_out)
        
        return output

class NewPyTorchTrainer:
    """全新的PyTorch訓練器"""
    
    def __init__(self, sequence_length=30, hidden_size=128, num_layers=2, dropout=0.3):
        self.sequence_length = sequence_length
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.dropout = dropout
        
        # 設備
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🚀 使用設備: {self.device}")
        
        # 模型組件
        self.model = None
        self.label_encoder = None
        self.scaler = None
        
    def load_and_preprocess_data(self, csv_file):
        """載入並預處理100%成功率的姿勢數據"""
        print("📊 載入100%成功率姿勢數據...")
        
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        print(f"數據形狀: {df.shape}")
        
        # MediaPipe 33個關鍵點
        landmark_names = [
            '鼻子', '左眼內角', '左眼中心', '左眼外角', '右眼內角', '右眼中心', '右眼外角',
            '左耳', '右耳', '嘴巴左角', '嘴巴右角', '左肩膀', '右肩膀', '左手肘', '右手肘',
            '左手腕', '右手腕', '左手小指', '右手小指', '左手食指', '右手食指', '左手拇指', '右手拇指',
            '左臀部', '右臀部', '左膝蓋', '右膝蓋', '左腳踝', '右腳踝', '左腳跟', '右腳跟', '左腳趾', '右腳趾'
        ]
        
        # 提取關鍵點特徵
        feature_columns = []
        for name in landmark_names:
            x_col = f'{name}_X座標'
            y_col = f'{name}_Y座標'
            if x_col in df.columns and y_col in df.columns:
                feature_columns.extend([x_col, y_col])
        
        # 提取特徵和標籤
        features = df[feature_columns].values
        labels = df['標籤'].values
        
        print(f"特徵維度: {features.shape}")
        print(f"標籤分布: {pd.Series(labels).value_counts()}")
        
        return features, labels
    
    def create_sequences(self, features, labels):
        """創建時序序列"""
        print("🔄 創建時序序列...")
        
        # 標籤編碼
        self.label_encoder = LabelEncoder()
        encoded_labels = self.label_encoder.fit_transform(labels)
        
        # 特徵標準化
        self.scaler = StandardScaler()
        scaled_features = self.scaler.fit_transform(features)
        
        # 創建序列
        sequences = []
        sequence_labels = []
        
        # 按標籤分組創建序列
        unique_labels = np.unique(encoded_labels)
        for label in unique_labels:
            label_indices = np.where(encoded_labels == label)[0]
            label_features = scaled_features[label_indices]
            
            # 滑動窗口
            for i in range(len(label_features) - self.sequence_length + 1):
                sequence = label_features[i:i + self.sequence_length]
                sequences.append(sequence)
                sequence_labels.append(label)
        
        sequences = np.array(sequences)
        sequence_labels = np.array(sequence_labels)
        
        print(f"生成序列: {sequences.shape}")
        print(f"序列標籤分布: {np.bincount(sequence_labels)}")
        
        return sequences, sequence_labels
    
    def train(self, csv_file, epochs=100, batch_size=32, learning_rate=0.001):
        """訓練模型"""
        print("🚀 開始訓練全新的PyTorch LSTM模型...")
        
        # 載入數據
        features, labels = self.load_and_preprocess_data(csv_file)
        
        # 創建序列
        sequences, sequence_labels = self.create_sequences(features, labels)
        
        # 數據分割
        X_train, X_test, y_train, y_test = train_test_split(
            sequences, sequence_labels, test_size=0.2, random_state=42,
            stratify=sequence_labels if len(np.unique(sequence_labels)) > 1 else None
        )
        
        # 創建數據載入器
        train_dataset = SOPDataset(X_train, y_train)
        test_dataset = SOPDataset(X_test, y_test)
        
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
        
        # 創建模型
        input_size = sequences.shape[2]  # 特徵維度
        num_classes = len(self.label_encoder.classes_)
        
        self.model = ImprovedSOPLSTM(
            input_size=input_size,
            hidden_size=self.hidden_size,
            num_layers=self.num_layers,
            num_classes=num_classes,
            dropout=self.dropout
        ).to(self.device)
        
        print(f"模型參數: {sum(p.numel() for p in self.model.parameters()):,}")
        
        # 優化器和損失函數
        optimizer = optim.AdamW(self.model.parameters(), lr=learning_rate, weight_decay=0.01)
        criterion = nn.CrossEntropyLoss()
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
        
        # 訓練循環
        best_acc = 0
        for epoch in range(epochs):
            # 訓練
            self.model.train()
            train_loss = 0
            train_correct = 0
            train_total = 0
            
            for batch_x, batch_y in train_loader:
                batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                
                optimizer.zero_grad()
                outputs = self.model(batch_x)
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                train_total += batch_y.size(0)
                train_correct += (predicted == batch_y).sum().item()
            
            # 驗證
            self.model.eval()
            test_loss = 0
            test_correct = 0
            test_total = 0
            
            with torch.no_grad():
                for batch_x, batch_y in test_loader:
                    batch_x, batch_y = batch_x.to(self.device), batch_y.to(self.device)
                    outputs = self.model(batch_x)
                    loss = criterion(outputs, batch_y)
                    
                    test_loss += loss.item()
                    _, predicted = torch.max(outputs.data, 1)
                    test_total += batch_y.size(0)
                    test_correct += (predicted == batch_y).sum().item()
            
            # 計算準確率
            train_acc = 100 * train_correct / train_total
            test_acc = 100 * test_correct / test_total
            
            # 學習率調整
            scheduler.step(test_loss)
            
            # 保存最佳模型
            if test_acc > best_acc:
                best_acc = test_acc
                self.save_model('best_new_pytorch_model.pth')
            
            # 打印進度
            if epoch % 10 == 0:
                print(f"Epoch {epoch:3d}: Train Acc: {train_acc:.2f}%, Test Acc: {test_acc:.2f}%, Best: {best_acc:.2f}%")
        
        print(f"\n🎉 訓練完成！最佳準確率: {best_acc:.2f}%")
        return best_acc
    
    def save_model(self, model_path):
        """保存模型"""
        # 創建模型資料夾
        os.makedirs('new_pytorch_models', exist_ok=True)
        
        # 保存模型
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'label_encoder': self.label_encoder,
            'scaler': self.scaler,
            'model_config': {
                'input_size': self.model.lstm.input_size,
                'hidden_size': self.hidden_size,
                'num_layers': self.num_layers,
                'num_classes': len(self.label_encoder.classes_),
                'dropout': self.dropout
            }
        }, f'new_pytorch_models/{model_path}')
        
        print(f"✅ 模型已保存: new_pytorch_models/{model_path}")

def main():
    """主函數"""
    # 創建訓練器
    trainer = NewPyTorchTrainer(
        sequence_length=30,
        hidden_size=128,
        num_layers=2,
        dropout=0.3
    )
    
    # 使用100%成功率的數據訓練
    csv_file = 'pose_output/1_pose_data_繁體中文_含時間_清理版_含標籤.csv'
    trainer.train(csv_file, epochs=100, batch_size=32, learning_rate=0.001)

if __name__ == "__main__":
    main()
