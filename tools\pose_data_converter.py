#!/usr/bin/env python3
"""
姿勢數據一站式轉換工具
將MediaPipe輸出的英文CSV檔案一次性轉換為完整的中文標籤版本

轉換流程:
英文原檔案 → 中文欄位 → 清理欄位 → 添加時間 → 添加標籤 → 最終檔案

使用方法:
python pose_data_converter.py --input 1_pose_data.csv --fps 29.97
"""

import pandas as pd
import os
from datetime import datetime

class PoseDataConverter:
    """姿勢數據轉換器"""

    def __init__(self, fps=29.97, add_labels=True):
        self.fps = fps
        self.add_labels = add_labels
        self.action_labels = self.create_action_labels() if add_labels else None
        
    def create_column_mapping(self):
        """創建英文到中文的欄位對應表"""
        
        # MediaPipe 33個關鍵點的中文名稱
        landmark_names = [
            '鼻子', '左眼內角', '左眼中心', '左眼外角', '右眼內角', '右眼中心', '右眼外角',
            '左耳', '右耳', '嘴巴左角', '嘴巴右角', '左肩膀', '右肩膀', '左手肘', '右手肘',
            '左手腕', '右手腕', '左手小指', '右手小指', '左手食指', '右手食指', '左手拇指', '右手拇指',
            '左臀部', '右臀部', '左膝蓋', '右膝蓋', '左腳踝', '右腳踝', '左腳跟', '右腳跟', '左腳趾', '右腳趾'
        ]
        
        # 基本欄位對應
        column_mapping = {
            'frame_idx': '幀數',
            'timestamp': '時間戳'
        }
        
        # 添加33個關鍵點的對應
        for i, name in enumerate(landmark_names):
            column_mapping[f'landmark_{i}_x'] = f'{name}_X座標'
            column_mapping[f'landmark_{i}_y'] = f'{name}_Y座標'
            column_mapping[f'landmark_{i}_z'] = f'{name}_Z座標'
            column_mapping[f'landmark_{i}_visibility'] = f'{name}_可見度'
        
        return column_mapping
    
    def create_action_labels(self):
        """創建動作標籤對應表"""
        
        action_labels = [
            (0, 1, "尋位"),
            (1, 4, "鎖第1顆螺絲"),
            (4, 5, "尋位"),
            (5, 6, "鎖第2顆螺絲"),
            (6, 8, "鎖第3顆螺絲"),
            (8, 9, "尋位"),
            (9, 10, "鎖第4顆螺絲"),
            (10, 14, "瑕疵(掉螺絲)"),
            (14, 16, "鎖第5顆螺絲"),
            (16, 17, "尋位"),
            (17, 19, "鎖第6顆螺絲"),
            (19, 20, "尋位"),
            (20, 22, "旋轉工件(順時針)"),
            (22, 23, "尋位"),
            (23, 24, "鎖第7顆螺絲"),
            (24, 26, "鎖第8顆螺絲"),
            (26, 28, "鎖第9顆螺絲"),
            (28, 31, "尋位"),
            (31, 32, "旋轉工件(順時針)"),
            (32, 35, "尋位"),
            (35, 36, "鎖第10顆螺絲"),
            (36, 37, "尋位"),
            (37, 39, "鎖第11顆螺絲"),
            (39, 40, "尋位"),
            (40, 42, "鎖第12顆螺絲"),
            (42, 43, "尋位"),
            (43, 45, "鎖第13顆螺絲"),
            (45, 46, "鎖第14顆螺絲"),
            (46, 47, "鎖第15顆螺絲"),
            (47, 48, "尋位"),
            (48, 50, "鎖第16顆螺絲"),
            (50, 52, "旋轉工件(順時針)"),
            (52, 53, "結束動作")
        ]
        
        return action_labels
    
    def assign_action_label(self, seconds):
        """根據秒數分配動作標籤"""
        
        for start_time, end_time, label in self.action_labels:
            if start_time <= seconds < end_time:
                return label
        
        return "未定義"
    
    def seconds_to_mmss(self, seconds):
        """將秒數轉換為 MM:SS.ff 格式"""
        minutes = int(seconds // 60)
        remaining_seconds = seconds % 60
        return f"{minutes:02d}:{remaining_seconds:05.2f}"
    
    def convert(self, input_file, output_file=None):
        """一站式轉換"""

        if output_file is None:
            base_name = os.path.splitext(input_file)[0]
            if self.add_labels:
                output_file = f"{base_name}_繁體中文_清理版_含時間_含標籤.csv"
            else:
                output_file = f"{base_name}_繁體中文_清理版_含時間.csv"
        
        print("🚀 開始姿勢數據一站式轉換...")
        print(f"📊 輸入檔案: {input_file}")
        print(f"💾 輸出檔案: {output_file}")
        print("="*60)
        
        # 步驟1: 讀取原始檔案
        print("📖 步驟1: 讀取原始英文CSV檔案...")
        try:
            df = pd.read_csv(input_file, encoding='utf-8-sig')
            print(f"✅ 成功讀取 {len(df)} 行數據，{len(df.columns)} 個欄位")
        except Exception as e:
            print(f"❌ 讀取檔案失敗: {e}")
            return False
        
        # 步驟2: 轉換欄位名稱為中文
        print("\n🔤 步驟2: 轉換欄位名稱為中文...")
        column_mapping = self.create_column_mapping()
        
        columns_to_rename = {}
        for eng_col, chi_col in column_mapping.items():
            if eng_col in df.columns:
                columns_to_rename[eng_col] = chi_col
        
        df = df.rename(columns=columns_to_rename)
        print(f"✅ 成功轉換 {len(columns_to_rename)} 個欄位名稱")
        
        # 步驟3: 清理不需要的欄位
        print("\n🗑️ 步驟3: 清理不需要的欄位...")
        columns_to_remove = [
            # 英文欄位
            'person_box_x1', 'person_box_y1', 'person_box_x2', 'person_box_y2', 'visibility_score',
            # 中文欄位  
            '人員框_X1', '人員框_Y1', '人員框_X2', '人員框_Y2', '可見度分數'
        ]
        
        existing_columns_to_remove = [col for col in columns_to_remove if col in df.columns]
        if existing_columns_to_remove:
            df = df.drop(columns=existing_columns_to_remove)
            print(f"✅ 成功移除 {len(existing_columns_to_remove)} 個不需要的欄位")
        else:
            print("ℹ️ 沒有找到需要移除的欄位")
        
        # 步驟4: 添加時間相關欄位
        print("\n🕐 步驟4: 添加時間相關欄位...")
        
        # 檢查幀數欄位
        frame_column = '幀數' if '幀數' in df.columns else 'frame_idx'
        if frame_column not in df.columns:
            print("❌ 找不到幀數欄位")
            return False
        
        # 計算影片秒數和時間
        df['影片秒數'] = df[frame_column] / self.fps
        df['影片時間'] = df['影片秒數'].apply(self.seconds_to_mmss)
        
        # 移除原始時間戳
        if '時間戳' in df.columns:
            df = df.drop(columns=['時間戳'])
            print("✅ 移除原始時間戳，添加影片秒數和影片時間")
        else:
            print("✅ 添加影片秒數和影片時間")
        
        # 步驟5: 添加動作標籤 (可選)
        if self.add_labels:
            print("\n🏷️ 步驟5: 添加SOP動作標籤...")
            df['標籤'] = df['影片秒數'].apply(self.assign_action_label)

            # 移除影片秒數欄位
            df = df.drop(columns=['影片秒數'])

            # 重新排列欄位順序
            time_columns = [frame_column, '影片時間', '標籤']
            other_columns = [col for col in df.columns if col not in time_columns]
            df = df[time_columns + other_columns]

            print("✅ 成功添加動作標籤")

            # 統計標籤分布
            label_counts = df['標籤'].value_counts()
            print(f"\n📊 標籤分布統計:")
            for label, count in label_counts.items():
                percentage = count / len(df) * 100
                print(f"  {label}: {count}幀 ({percentage:.1f}%)")
        else:
            print("\n⏭️ 步驟5: 跳過動作標籤添加...")

            # 保留影片秒數欄位，重新排列欄位順序
            time_columns = [frame_column, '影片秒數', '影片時間']
            other_columns = [col for col in df.columns if col not in time_columns]
            df = df[time_columns + other_columns]

            print("✅ 保留影片秒數欄位，未添加動作標籤")
        
        # 步驟6: 保存最終檔案
        print(f"\n💾 步驟6: 保存最終檔案...")
        try:
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"✅ 轉換完成！檔案已保存: {output_file}")
            
            # 顯示最終結果
            print(f"\n📋 最終檔案資訊:")
            print(f"  總行數: {len(df)}")
            print(f"  總欄位: {len(df.columns)}")
            print(f"  影片總長: {df['影片時間'].iloc[-1]}")

            if self.add_labels:
                print(f"  動作類型: {len(label_counts)} 種")
                # 顯示前幾行示例
                print(f"\n📋 轉換結果示例:")
                print(df[[frame_column, '影片時間', '標籤']].head(10).to_string(index=False))
            else:
                print(f"  包含標籤: 否")
                # 顯示前幾行示例
                print(f"\n📋 轉換結果示例:")
                print(df[[frame_column, '影片秒數', '影片時間']].head(10).to_string(index=False))
            
            return True
            
        except Exception as e:
            print(f"❌ 保存檔案失敗: {e}")
            return False

def main():
    """主函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description='姿勢數據一站式轉換工具')
    parser.add_argument('--input', '-i', type=str, required=True,
                       help='輸入的英文CSV檔案路徑')
    parser.add_argument('--output', '-o', type=str, 
                       help='輸出CSV檔案路徑 (可選)')
    parser.add_argument('--fps', type=float, default=29.97,
                       help='影片幀率 (預設: 29.97)')
    parser.add_argument('--add-labels', action='store_true',
                       help='添加SOP動作標籤 (預設: 不添加)')
    parser.add_argument('--no-labels', dest='add_labels', action='store_false',
                       help='不添加動作標籤，只進行基本轉換')
    parser.set_defaults(add_labels=False)
    
    args = parser.parse_args()
    
    # 檢查輸入檔案是否存在
    if not os.path.exists(args.input):
        print(f"❌ 輸入檔案不存在: {args.input}")
        return
    
    # 創建轉換器並執行轉換
    converter = PoseDataConverter(fps=args.fps, add_labels=args.add_labels)
    success = converter.convert(args.input, args.output)

    if success:
        print("\n🎉 一站式轉換完成！")
        print("📊 您現在有了處理好的數據:")
        print("  ✅ 中文欄位名稱")
        print("  ✅ 乾淨的數據格式")
        print("  ✅ 精確的時間標記")
        if args.add_labels:
            print("  ✅ 完整的動作標籤")
            print("  ✅ 可直接用於模型訓練")
        else:
            print("  ⏭️ 未添加動作標籤")
            print("  ✅ 可用於其他分析或手動標記")
    else:
        print("\n❌ 轉換失敗！")

if __name__ == "__main__":
    main()
