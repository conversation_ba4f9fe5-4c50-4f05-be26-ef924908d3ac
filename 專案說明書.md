# SOP動作識別與合規檢查系統 - 完整說明書

## 📋 專案概述

本專案是一個基於AI的工業SOP（標準作業程序）監控系統，能夠自動檢測工人的動作並驗證是否按照標準流程執行16個螺絲的鎖緊作業。

### 🎯 主要功能
- **姿勢檢測**: 使用MediaPipe提取工人身體關鍵點
- **動作識別**: 使用LSTM模型識別3類動作（鎖螺絲/旋轉工件/其他動作）
- **位置檢測**: 判斷工人手部在16個螺絲位置中的哪一個
- **合規檢查**: 自動統計16個螺絲位置的完成狀態
- **實時監控**: 可應用於實際工業生產線

---

## 📁 專案結構詳解

### 🎬 **videos/** - 原始影片資料
```
videos/
├── 1.mp4          # 訓練影片 - 包含完整SOP流程的標準作業影片
└── 2.mp4          # 測試影片 - 用於驗證系統準確性的測試影片
```

### 🔍 **1-pose_output/** - 訓練影片處理結果
```
1-pose_output/
├── 1_perfect_test.mp4                              # 處理後的影片（含姿勢標記）
├── 1_pose_data.csv                                 # 原始姿勢數據（英文欄位）
├── 1_pose_data.json                               # JSON格式姿勢數據
├── 1_pose_data.npy                                # NumPy格式姿勢數據
├── 1_pose_data_繁體中文_清理版_含時間_含標籤.csv      # 🔥 標籤訓練數據（含SOP動作標籤）
├── 1_pose_data_3類平衡版.csv                       # 🔥 最終訓練數據（3類平衡版本）
└── pose_extractor_20250715_102206.log             # 姿勢提取日誌
```

**重要檔案說明**:
- **1_pose_data_3類平衡版.csv**: 最終用於訓練LSTM模型的數據，包含3類動作（鎖螺絲/旋轉工件/其他動作），每類300幀，完全平衡

### 🧪 **2-pose/** - 測試影片處理結果
```
2-pose/
├── 2_processed.mp4                                 # 處理後的測試影片
├── 2_pose_data.csv                                # 原始姿勢數據
├── 2_pose_data.json                               # JSON格式數據
├── 2_pose_data.npy                                # NumPy格式數據
├── 2_pose_data_繁體中文_清理版_含時間.csv            # 🔥 測試用姿勢數據
├── sop_compliance_report_20250716_101357.csv      # 舊版SOP檢查結果
├── sop_compliance_report_20250716_112732.csv      # 🔥 最新SOP檢查結果
├── sop_compliance_summary_20250716_101357.json    # 舊版合規摘要
├── sop_compliance_summary_20250716_112732.json    # 🔥 最新合規摘要
└── pose_extractor_20250715_161616.log             # 姿勢提取日誌
```

**重要檔案說明**:
- **sop_compliance_report_20250716_112732.csv**: 最新的SOP合規檢查詳細結果，包含每幀的動作識別和16個螺絲位置的完成狀態
- **sop_compliance_summary_20250716_112732.json**: 合規檢查摘要，顯示最終的11/16螺絲完成率（68.8%）

### 🤖 **models/** - AI模型檔案
```
models/
├── sop_3class_action_model.h5      # 🔥 主要LSTM模型（3類動作識別）
├── sop_3class_config.json          # 模型配置檔案
├── sop_3class_label_encoder.pkl    # 標籤編碼器
├── sop_3class_scaler.pkl           # 特徵標準化器
└── yolov8n.pt                      # YOLO人員檢測模型
```

**模型說明**:
- **sop_3class_action_model.h5**: 核心LSTM模型，100%測試準確率，可識別3類動作
- **標籤編碼器**: 將動作名稱轉換為數字編碼
- **特徵標準化器**: 將姿勢座標標準化為模型可用格式

### 🛠️ **tools/** - 工具程式
```
tools/
├── pose_extractor.py           # 🔥 姿勢提取器 - 從影片提取MediaPipe姿勢數據
├── pose_data_converter.py      # 數據轉換器 - 轉換姿勢數據格式
├── screw_monitor.py            # 螺絲監控器 - 實時監控工具
├── yolo_pose_gui.py            # YOLO+姿勢檢測GUI
└── new_pytorch_trainer.py      # PyTorch訓練器
```

### ⚙️ **configs/** - 配置檔案
```
configs/
├── sop_config.json             # SOP監控配置
├── task_area_config.json       # 工作區域配置
└── test_pose_config.json       # 姿勢測試配置
```

### 📊 **results/** - 測試結果
```
results/
├── screw_monitor_test_results.csv         # 螺絲監控測試結果
└── screw_monitor_test_visualization.png   # 測試結果視覺化
```

---

## 🚀 **核心系統檔案**

### **sop_compliance_system.py** - 🔥 主要SOP合規檢查系統
這是整個專案的核心檔案，實現完整的SOP合規檢查功能：

**主要功能**:
1. **載入3類LSTM模型**: 識別鎖螺絲/旋轉工件/其他動作
2. **位置檢測**: 將工作區域劃分為4x4網格，對應16個螺絲位置
3. **動作識別**: 逐幀分析影片，識別當前動作
4. **狀態追蹤**: 記錄16個螺絲位置的完成狀態
5. **合規報告**: 生成詳細的SOP合規檢查報告

**使用方法**:
```bash
python sop_compliance_system.py
```

**輸出結果**:
- 詳細CSV報告: 每幀的動作識別和螺絲狀態
- JSON摘要: 最終的合規率和完成狀態

---

## 🔬 **專業模型資料夾**

### **intelligent_work_monitor/** - 完整監控系統
包含完整的工業監控系統，具備實時監控能力。

### **pytorch_models/** - PyTorch模型實現
包含使用PyTorch框架實現的動作識別模型。

### **stgcn_models/** - ST-GCN模型
包含時空圖卷積網路模型，用於更複雜的動作識別。

### **actionai_models/** - ActionAI模型
包含ActionAI框架的動作識別實現。

### **force_detection/** - 力量檢測系統
```
force_detection/
├── force_detection_analyzer.py        # 力量檢測分析器
├── force_analysis_interpreter.py      # 力量分析解釋器
├── force_analysis_result.csv          # 力量分析結果
├── force_detection_analysis.png       # 力量檢測視覺化
├── detailed_force_timeline.png        # 詳細力量時間軸
└── force_interpretation_report.txt    # 力量解釋報告
```

---

## 📖 **使用指南**

### 🎯 **快速開始**
1. **執行SOP檢查**:
   ```bash
   python sop_compliance_system.py
   ```

2. **查看結果**:
   - 詳細結果: `2-pose/sop_compliance_report_*.csv`
   - 摘要報告: `2-pose/sop_compliance_summary_*.json`

### 🔧 **自定義使用**
1. **更換測試影片**: 將新影片放入`videos/`資料夾
2. **提取姿勢數據**: 使用`tools/pose_extractor.py`
3. **執行SOP檢查**: 運行主系統檔案

### 📊 **結果解讀**
- **✅**: 螺絲位置已完成
- **❌**: 螺絲位置未完成
- **合規率**: 完成螺絲數量/總螺絲數量 × 100%

---

## 🎯 **系統特點**

### ✅ **優勢**
1. **高準確率**: LSTM模型達到100%測試準確率
2. **實時監控**: 可應用於實際生產線
3. **完整追蹤**: 記錄16個螺絲位置的詳細狀態
4. **簡化邏輯**: 檢測到鎖螺絲動作即標記位置完成
5. **工業應用**: 符合實際SOP監控需求

### 🎨 **技術架構**
- **姿勢檢測**: MediaPipe
- **動作識別**: LSTM深度學習
- **位置檢測**: 座標映射
- **數據處理**: Pandas + NumPy
- **模型框架**: TensorFlow/Keras

---

## 📝 **維護說明**

### 🔄 **定期更新**
- 模型重訓練: 當有新的SOP流程時
- 位置校正: 根據實際工作環境調整
- 參數優化: 根據使用反饋調整檢測參數

### 🐛 **故障排除**
- 檢查模型檔案是否完整
- 確認影片格式支援
- 驗證姿勢檢測品質

---

**📞 技術支援**: 如有問題請參考各資料夾內的README檔案或聯繫開發團隊。
