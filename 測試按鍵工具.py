#!/usr/bin/env python3
"""
測試按鍵工具 - 診斷OpenCV按鍵問題
"""

import cv2
import numpy as np

def test_keys():
    """測試按鍵檢測"""
    print('🔍 OpenCV按鍵測試工具')
    print('=' * 40)
    print('📋 測試說明:')
    print('   - 視窗開啟後，點擊視窗獲得焦點')
    print('   - 按任意鍵測試檢測功能')
    print('   - 按 ESC 或 Q 退出')
    print('   - 觀察終端輸出的按鍵碼')
    
    # 創建一個簡單的測試圖像
    img = np.zeros((400, 600, 3), dtype=np.uint8)
    img[:] = (50, 50, 50)  # 深灰色背景
    
    # 創建視窗
    window_name = '按鍵測試'
    cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
    cv2.resizeWindow(window_name, 600, 400)
    
    print(f'\n🖥️ 視窗 "{window_name}" 已開啟')
    print('💡 請點擊視窗，然後按任意鍵...')
    
    key_count = 0
    
    while True:
        # 創建顯示圖像
        display_img = img.copy()
        
        # 顯示說明文字
        cv2.putText(display_img, 'OpenCV Key Test', (50, 50),
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        cv2.putText(display_img, 'Click window and press any key', (50, 100),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (200, 200, 200), 1)
        cv2.putText(display_img, f'Keys detected: {key_count}', (50, 150),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 1)
        cv2.putText(display_img, 'Press ESC or Q to exit', (50, 200),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 200, 255), 1)
        
        cv2.imshow(window_name, display_img)
        
        # 檢測按鍵
        key = cv2.waitKey(30) & 0xFF
        
        if key != 255:  # 有按鍵
            key_count += 1
            print(f'🔍 按鍵 #{key_count}: 碼={key}', end='')
            
            if 32 <= key <= 126:  # 可顯示字符
                print(f' 字符="{chr(key)}"', end='')
            
            print()  # 換行
            
            # 特殊按鍵處理
            if key == 27 or key == ord('q'):  # ESC 或 Q
                print('👋 退出測試')
                break
            elif key == ord('a'):
                print('   ✅ A鍵檢測成功！')
            elif key == ord('d'):
                print('   ✅ D鍵檢測成功！')
            elif key >= ord('1') and key <= ord('9'):
                print(f'   ✅ 數字鍵 {chr(key)} 檢測成功！')
        
        # 檢查視窗是否被關閉
        if cv2.getWindowProperty(window_name, cv2.WND_PROP_VISIBLE) < 1:
            print('❌ 視窗被關閉')
            break
    
    cv2.destroyAllWindows()
    print(f'\n📊 測試結果: 共檢測到 {key_count} 個按鍵')
    
    if key_count == 0:
        print('❌ 沒有檢測到任何按鍵，可能的原因:')
        print('   1. 視窗沒有獲得焦點')
        print('   2. OpenCV版本問題')
        print('   3. 系統按鍵攔截')
        print('   4. 視窗管理器問題')
    else:
        print('✅ 按鍵檢測正常工作')

if __name__ == "__main__":
    test_keys()
