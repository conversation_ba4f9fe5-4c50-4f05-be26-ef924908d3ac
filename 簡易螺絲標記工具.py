#!/usr/bin/env python3
"""
簡易螺絲標記工具
更簡單的操作方式，解決按鍵無反應問題
"""

import cv2
import json
import numpy as np
import os
from datetime import datetime

class SimpleMarker:
    def __init__(self):
        self.video_path = None
        self.cap = None
        self.total_frames = 0
        self.fps = 30
        self.current_frame = 0
        
        # 標記數據
        self.screw_positions = {}  # {hole_id: position_data}
        self.current_hole_id = 1
        
        # 當前標記狀態
        self.current_image = None
        self.display_image = None
        self.drawing = False
        self.start_point = None
        
    def load_video(self, video_path):
        """載入影片"""
        self.video_path = video_path
        self.cap = cv2.VideoCapture(video_path)
        
        if not self.cap.isOpened():
            print('❌ 無法開啟影片')
            return False
        
        self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
        self.fps = self.cap.get(cv2.CAP_PROP_FPS)
        
        print(f'✅ 影片載入成功')
        print(f'📊 總幀數: {self.total_frames}')
        print(f'🎬 幀率: {self.fps:.1f} FPS')
        print(f'⏱️ 總時長: {self.total_frames/self.fps:.1f}秒')
        
        return True
    
    def goto_frame(self, frame_number):
        """跳到指定幀"""
        if frame_number < 0:
            frame_number = 0
        elif frame_number >= self.total_frames:
            frame_number = self.total_frames - 1
        
        self.current_frame = frame_number
        self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
        ret, frame = self.cap.read()
        
        if ret:
            self.current_image = frame.copy()
            self.display_image = frame.copy()
            self.draw_existing_marks()
            return True
        return False
    
    def draw_existing_marks(self):
        """繪製已有的標記"""
        for hole_id, pos_data in self.screw_positions.items():
            color = self.get_hole_color(hole_id)
            
            # 繪製矩形
            cv2.rectangle(self.display_image,
                         (pos_data['x_min'], pos_data['y_min']),
                         (pos_data['x_max'], pos_data['y_max']),
                         color, 2)
            
            # 繪製編號
            cv2.circle(self.display_image,
                      (pos_data['center_x'], pos_data['center_y']),
                      15, color, -1)
            cv2.putText(self.display_image, str(hole_id),
                       (pos_data['center_x'] - 6, pos_data['center_y'] + 4),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    def get_hole_color(self, hole_id):
        """根據螺絲孔ID獲取顏色"""
        colors = [
            (0, 255, 0),    # 綠色
            (255, 0, 0),    # 藍色  
            (0, 0, 255),    # 紅色
            (255, 255, 0),  # 青色
            (255, 0, 255),  # 洋紅
            (0, 255, 255),  # 黃色
        ]
        return colors[hole_id % len(colors)]
    
    def mouse_callback(self, event, x, y, flags, param):
        """滑鼠事件處理"""
        if event == cv2.EVENT_LBUTTONDOWN:
            self.drawing = True
            self.start_point = (x, y)
            print(f'🖱️ 開始標記螺絲孔 {self.current_hole_id}')
            
        elif event == cv2.EVENT_MOUSEMOVE and self.drawing:
            temp_image = self.display_image.copy()
            color = self.get_hole_color(self.current_hole_id)
            cv2.rectangle(temp_image, self.start_point, (x, y), color, 2)
            
            text = f'螺絲孔 {self.current_hole_id}'
            cv2.putText(temp_image, text,
                       (self.start_point[0], self.start_point[1] - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
            
            cv2.imshow('簡易螺絲標記工具', temp_image)
            
        elif event == cv2.EVENT_LBUTTONUP and self.drawing:
            self.drawing = False
            if abs(x - self.start_point[0]) > 10 and abs(y - self.start_point[1]) > 10:
                self.add_hole_mark(self.start_point, (x, y))
    
    def add_hole_mark(self, start_point, end_point):
        """添加螺絲孔標記"""
        x1, y1 = start_point
        x2, y2 = end_point
        
        x_min, x_max = min(x1, x2), max(x1, x2)
        y_min, y_max = min(y1, y2), max(y1, y2)
        center_x = (x_min + x_max) // 2
        center_y = (y_min + y_max) // 2
        
        self.screw_positions[self.current_hole_id] = {
            'hole_id': self.current_hole_id,
            'frame': self.current_frame,
            'time_seconds': self.current_frame / self.fps,
            'x_min': x_min,
            'x_max': x_max,
            'y_min': y_min,
            'y_max': y_max,
            'center_x': center_x,
            'center_y': center_y,
            'width': x_max - x_min,
            'height': y_max - y_min
        }
        
        time_str = f"{int(self.current_frame / self.fps // 60):02d}:{int(self.current_frame / self.fps % 60):02d}"
        print(f'✅ 螺絲孔 {self.current_hole_id} 已標記在 {time_str} (第{self.current_frame}幀)')
        
        # 自動切換到下一個螺絲孔
        if self.current_hole_id < 16:
            self.current_hole_id += 1
            print(f'🎯 切換到螺絲孔 {self.current_hole_id}')
        
        self.draw_existing_marks()
    
    def start_marking(self):
        """開始標記流程"""
        print('\n🎯 簡易螺絲標記工具')
        print('=' * 50)
        
        print('📋 操作說明:')
        print('🖱️  拖拽滑鼠 : 圈選螺絲孔位置')
        print('⌨️  按鍵操作:')
        print('   a/d : 前後移動幀')
        print('   w/s : 快速跳躍(5秒)')
        print('   +/- : 切換螺絲孔編號')
        print('   空白鍵 : 保存配置檔案')
        print('   ESC : 退出程式')
        
        # 跳到第100幀開始
        self.goto_frame(100)
        
        cv2.namedWindow('簡易螺絲標記工具', cv2.WINDOW_NORMAL)
        cv2.resizeWindow('簡易螺絲標記工具', 1200, 800)
        cv2.setMouseCallback('簡易螺絲標記工具', self.mouse_callback)
        
        print('\n🖥️ OpenCV視窗已開啟，請點擊視窗獲得焦點')
        print('💡 然後按 a/d 鍵測試是否有反應')
        
        while True:
            # 更新顯示
            temp_image = self.display_image.copy()
            
            # 顯示狀態資訊
            time_str = f"{int(self.current_frame / self.fps // 60):02d}:{int(self.current_frame / self.fps % 60):02d}"
            status = f'時間: {time_str} | 幀: {self.current_frame}/{self.total_frames} | 已標記: {len(self.screw_positions)}/16'
            cv2.putText(temp_image, status, (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
            
            # 顯示當前標記模式
            mode_text = f'當前標記: 螺絲孔 {self.current_hole_id} (拖拽滑鼠圈選)'
            cv2.putText(temp_image, mode_text, (10, 60),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            
            # 顯示已標記的螺絲孔清單
            marked_holes = sorted(self.screw_positions.keys())
            holes_text = f'已標記螺絲孔: {marked_holes}'
            cv2.putText(temp_image, holes_text, (10, 90),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
            
            cv2.imshow('簡易螺絲標記工具', temp_image)
            
            key = cv2.waitKey(30) & 0xFF
            
            # 調試：顯示按鍵碼
            if key != 255:
                print(f'🔍 按鍵: {key} ({chr(key) if 32 <= key <= 126 else "特殊鍵"})')
            
            if key == 27:  # ESC
                print('👋 退出程式')
                break
            elif key == ord('a'):  # 向前
                print('⬅️ 向前移動一幀')
                self.goto_frame(self.current_frame - 1)
            elif key == ord('d'):  # 向後
                print('➡️ 向後移動一幀')
                self.goto_frame(self.current_frame + 1)
            elif key == ord('w'):  # 快速向前
                print('⏪ 快速向前跳躍5秒')
                self.goto_frame(self.current_frame - int(5 * self.fps))
            elif key == ord('s'):  # 快速向後
                print('⏩ 快速向後跳躍5秒')
                self.goto_frame(self.current_frame + int(5 * self.fps))
            elif key == ord('+') or key == ord('='):  # 下一個螺絲孔
                if self.current_hole_id < 16:
                    self.current_hole_id += 1
                    print(f'🎯 切換到螺絲孔 {self.current_hole_id}')
            elif key == ord('-'):  # 上一個螺絲孔
                if self.current_hole_id > 1:
                    self.current_hole_id -= 1
                    print(f'🎯 切換到螺絲孔 {self.current_hole_id}')
            elif key == ord(' '):  # 空白鍵保存
                if len(self.screw_positions) >= 16:
                    self.save_configuration()
                    print('✅ 配置檔案保存成功！')
                else:
                    print(f'⚠️ 請先標記所有16個螺絲孔 (目前: {len(self.screw_positions)}/16)')
        
        cv2.destroyAllWindows()
        return len(self.screw_positions) >= 16
    
    def save_configuration(self):
        """保存配置檔案"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 準備匯出數據
        export_data = {
            'metadata': {
                'timestamp': timestamp,
                'video_path': self.video_path,
                'total_holes': len(self.screw_positions),
                'software': '簡易螺絲標記工具 v1.0',
                'description': '16個螺絲孔位置配置'
            },
            'screw_positions': self.screw_positions
        }
        
        # 保存JSON配置檔
        json_file = f'簡易螺絲配置_{timestamp}.json'
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        print(f'\n📁 配置檔案已匯出: {json_file}')
        return True

def main():
    """主程式"""
    print('🎬 簡易螺絲標記工具')
    print('=' * 50)
    
    # 選擇影片
    video_path = input('請輸入影片路徑 (預設: videos/2.mp4): ').strip()
    if not video_path:
        video_path = 'videos/2.mp4'
    
    if not os.path.exists(video_path):
        print(f'❌ 找不到影片檔案: {video_path}')
        return
    
    # 開始標記
    marker = SimpleMarker()
    
    if marker.load_video(video_path):
        if marker.start_marking():
            print('\n🎉 螺絲標記完成！')
        else:
            print('\n❌ 標記未完成')

if __name__ == "__main__":
    main()
