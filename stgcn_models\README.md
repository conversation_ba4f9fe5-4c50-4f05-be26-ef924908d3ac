# ST-GCN 模型

## 模型資訊
- **模型類型**: 時空圖卷積網絡 (Spatial-Temporal Graph Convolutional Network)
- **準確率**: 52.17%
- **訓練數據**: 111個序列樣本
- **序列長度**: 15幀 (0.5秒)
- **關鍵點**: 33個MediaPipe關鍵點

## 檔案說明
- `best_stgcn_model.pth` - 訓練好的ST-GCN模型

## 模型特點
- ✅ 能區分6種不同動作
- ✅ 預測結果分布合理
- ✅ 解決了LSTM過擬合問題
- ⚠️ 準確率中等，有改進空間

## 預測結果 (視頻2)
- 鎖第6顆螺絲: 31.7%
- 鎖第8顆螺絲: 28.9%
- 旋轉工件: 16.6%
- 鎖第11顆螺絲: 14.6%
- 其他動作: 8.2%

## 使用方法
```python
# 在 pytorch_lstm_trainer.py 中使用
python pytorch_lstm_trainer.py predict_video2
```
