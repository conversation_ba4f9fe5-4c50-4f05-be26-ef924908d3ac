#!/usr/bin/env python3
"""
出力檢測結果解讀器
專門分析16顆螺絲+3次旋轉的出力模式
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# 設置中文字體
plt.rcParams['font.sans-serif'] = ['Microsoft JhengHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

class ForceAnalysisInterpreter:
    """出力檢測結果解讀器"""
    
    def __init__(self):
        # SOP動作定義
        self.screw_actions = [
            "鎖第1顆螺絲", "鎖第2顆螺絲", "鎖第3顆螺絲", "鎖第4顆螺絲",
            "鎖第5顆螺絲", "鎖第6顆螺絲", "鎖第7顆螺絲", "鎖第8顆螺絲",
            "鎖第9顆螺絲", "鎖第10顆螺絲", "鎖第11顆螺絲", "鎖第12顆螺絲",
            "鎖第13顆螺絲", "鎖第14顆螺絲", "鎖第15顆螺絲", "鎖第16顆螺絲"
        ]
        
        self.rotation_actions = ["旋轉工件(順時針)"]
        self.other_actions = ["尋位", "瑕疵(掉螺絲)", "結束動作"]
    
    def load_analysis_result(self, csv_file):
        """載入分析結果"""
        print(f"📊 載入出力檢測結果: {csv_file}")
        
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        print(f"✅ 成功載入 {len(df)} 行數據")
        
        return df
    
    def analyze_force_by_action(self, df):
        """按動作類型分析出力模式"""
        print("🔍 分析各動作的出力模式...")
        
        # 按標籤分組分析
        action_analysis = {}
        
        for action in self.screw_actions + self.rotation_actions + self.other_actions:
            action_data = df[df['標籤'] == action]
            
            if len(action_data) > 0:
                rule_force_rate = action_data['規則檢測_出力'].mean()
                ml_force_rate = action_data['ML預測_出力'].mean()
                avg_probability = action_data['出力機率'].mean()
                frame_count = len(action_data)
                
                action_analysis[action] = {
                    '幀數': frame_count,
                    '規則檢測出力率': rule_force_rate,
                    'ML預測出力率': ml_force_rate,
                    '平均出力機率': avg_probability
                }
        
        return action_analysis
    
    def create_detailed_timeline(self, df):
        """創建詳細的時間軸分析"""
        print("📈 創建詳細時間軸分析...")
        
        fig, axes = plt.subplots(4, 1, figsize=(20, 16))
        
        # 時間軸 (使用幀數)
        time_axis = df['幀數'].values
        
        # 圖1: 出力檢測對比
        axes[0].plot(time_axis, df['規則檢測_出力'].astype(int), 'b-', label='規則檢測', alpha=0.8, linewidth=2)
        axes[0].plot(time_axis, df['ML預測_出力'].astype(int) + 0.05, 'r-', label='ML預測', alpha=0.8, linewidth=2)
        axes[0].set_ylabel('出力狀態 (0=無, 1=有)')
        axes[0].set_title('出力檢測結果對比')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        axes[0].set_ylim(-0.1, 1.2)
        
        # 圖2: 出力機率
        axes[1].plot(time_axis, df['出力機率'], 'g-', alpha=0.7, linewidth=1)
        axes[1].axhline(y=0.5, color='orange', linestyle='--', alpha=0.7, label='50%閾值')
        axes[1].set_ylabel('出力機率')
        axes[1].set_title('出力機率變化')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        axes[1].set_ylim(0, 1)
        
        # 圖3: 動作標籤時間軸
        unique_labels = df['標籤'].unique()
        label_colors = plt.cm.Set3(np.linspace(0, 1, len(unique_labels)))
        label_to_color = dict(zip(unique_labels, label_colors))
        
        # 為每個標籤創建時間段
        current_label = None
        start_frame = 0
        
        for i, (frame, label) in enumerate(zip(df['幀數'], df['標籤'])):
            if label != current_label:
                if current_label is not None:
                    # 繪製前一個標籤的時間段
                    axes[2].barh(0, frame - start_frame, left=start_frame, 
                               color=label_to_color[current_label], alpha=0.7, 
                               edgecolor='black', linewidth=0.5)
                    
                    # 添加標籤文字 (只在較長的時間段添加)
                    if frame - start_frame > 50:
                        axes[2].text(start_frame + (frame - start_frame)/2, 0, current_label, 
                                   ha='center', va='center', fontsize=8, rotation=0)
                
                current_label = label
                start_frame = frame
        
        # 處理最後一個標籤
        if current_label is not None:
            final_frame = df['幀數'].iloc[-1]
            axes[2].barh(0, final_frame - start_frame, left=start_frame, 
                       color=label_to_color[current_label], alpha=0.7,
                       edgecolor='black', linewidth=0.5)
            if final_frame - start_frame > 50:
                axes[2].text(start_frame + (final_frame - start_frame)/2, 0, current_label, 
                           ha='center', va='center', fontsize=8, rotation=0)
        
        axes[2].set_ylabel('動作標籤')
        axes[2].set_title('SOP動作時間軸')
        axes[2].set_ylim(-0.5, 0.5)
        axes[2].set_yticks([])
        
        # 圖4: 螺絲鎖緊檢測重點分析
        screw_mask = df['標籤'].str.contains('鎖第.*螺絲', na=False)
        rotation_mask = df['標籤'].str.contains('旋轉工件', na=False)
        
        # 螺絲鎖緊時的出力
        axes[3].scatter(time_axis[screw_mask], df.loc[screw_mask, '出力機率'], 
                       c='red', alpha=0.6, s=20, label='鎖螺絲時刻')
        
        # 旋轉工件時的出力
        axes[3].scatter(time_axis[rotation_mask], df.loc[rotation_mask, '出力機率'], 
                       c='blue', alpha=0.6, s=20, label='旋轉工件時刻')
        
        # 其他時刻
        other_mask = ~(screw_mask | rotation_mask)
        axes[3].scatter(time_axis[other_mask], df.loc[other_mask, '出力機率'], 
                       c='gray', alpha=0.3, s=10, label='其他時刻')
        
        axes[3].axhline(y=0.5, color='orange', linestyle='--', alpha=0.7, label='50%閾值')
        axes[3].set_ylabel('出力機率')
        axes[3].set_xlabel('幀數')
        axes[3].set_title('螺絲鎖緊 vs 旋轉工件 出力對比')
        axes[3].legend()
        axes[3].grid(True, alpha=0.3)
        axes[3].set_ylim(0, 1)
        
        plt.tight_layout()
        plt.savefig('detailed_force_timeline.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ 詳細時間軸圖表已保存為 detailed_force_timeline.png")
    
    def analyze_screw_tightening_patterns(self, df):
        """分析16顆螺絲的鎖緊模式"""
        print("🔩 分析16顆螺絲的鎖緊模式...")
        
        screw_analysis = {}
        
        for i in range(1, 17):
            screw_label = f"鎖第{i}顆螺絲"
            screw_data = df[df['標籤'] == screw_label]
            
            if len(screw_data) > 0:
                # 計算出力統計
                rule_force_frames = screw_data['規則檢測_出力'].sum()
                ml_force_frames = screw_data['ML預測_出力'].sum()
                total_frames = len(screw_data)
                avg_probability = screw_data['出力機率'].mean()
                max_probability = screw_data['出力機率'].max()
                
                # 計算出力持續性 (連續出力幀數)
                force_sequences = []
                current_sequence = 0
                
                for force in screw_data['ML預測_出力']:
                    if force:
                        current_sequence += 1
                    else:
                        if current_sequence > 0:
                            force_sequences.append(current_sequence)
                            current_sequence = 0
                
                if current_sequence > 0:
                    force_sequences.append(current_sequence)
                
                screw_analysis[f"螺絲{i}"] = {
                    '總幀數': total_frames,
                    '規則檢測出力幀數': rule_force_frames,
                    'ML預測出力幀數': ml_force_frames,
                    '規則檢測出力率': rule_force_frames / total_frames if total_frames > 0 else 0,
                    'ML預測出力率': ml_force_frames / total_frames if total_frames > 0 else 0,
                    '平均出力機率': avg_probability,
                    '最大出力機率': max_probability,
                    '出力序列數': len(force_sequences),
                    '最長連續出力': max(force_sequences) if force_sequences else 0
                }
        
        return screw_analysis
    
    def generate_interpretation_report(self, df, action_analysis, screw_analysis):
        """生成解讀報告"""
        print("📋 生成出力檢測解讀報告...")
        
        report = []
        report.append("=" * 80)
        report.append("🔧 出力檢測結果解讀報告")
        report.append("=" * 80)
        
        # 整體統計
        total_frames = len(df)
        rule_force_frames = df['規則檢測_出力'].sum()
        ml_force_frames = df['ML預測_出力'].sum()
        
        report.append(f"\n📊 整體統計:")
        report.append(f"  總幀數: {total_frames}")
        report.append(f"  規則檢測出力: {rule_force_frames} 幀 ({rule_force_frames/total_frames*100:.1f}%)")
        report.append(f"  ML預測出力: {ml_force_frames} 幀 ({ml_force_frames/total_frames*100:.1f}%)")
        
        # 螺絲鎖緊分析
        report.append(f"\n🔩 16顆螺絲鎖緊分析:")
        report.append(f"{'螺絲':<8} {'總幀數':<8} {'出力率':<8} {'出力機率':<10} {'連續出力':<10}")
        report.append("-" * 60)
        
        for screw_name, data in screw_analysis.items():
            report.append(f"{screw_name:<8} {data['總幀數']:<8} {data['ML預測出力率']*100:>6.1f}% {data['平均出力機率']:>8.3f} {data['最長連續出力']:>8}")
        
        # 動作類型分析
        report.append(f"\n🎯 動作類型出力分析:")
        report.append(f"{'動作類型':<15} {'幀數':<8} {'出力率':<8} {'出力機率':<10}")
        report.append("-" * 50)
        
        for action, data in action_analysis.items():
            if data['幀數'] > 0:
                report.append(f"{action:<15} {data['幀數']:<8} {data['ML預測出力率']*100:>6.1f}% {data['平均出力機率']:>8.3f}")
        
        # 解讀說明
        report.append(f"\n💡 波形圖解讀說明:")
        report.append(f"  📈 出力機率波形:")
        report.append(f"    - 高峰 (>0.5): 檢測到明顯出力動作")
        report.append(f"    - 低谷 (<0.3): 無出力或輕微動作")
        report.append(f"    - 中等 (0.3-0.5): 輕微出力或準備動作")
        report.append(f"  🔩 螺絲鎖緊特徵:")
        report.append(f"    - 應該出現持續的高出力機率")
        report.append(f"    - 出力率應該 >70% 表示有效鎖緊")
        report.append(f"  🔄 旋轉工件特徵:")
        report.append(f"    - 會有手部位移，但出力相對較小")
        report.append(f"    - 出力機率波動較大")
        
        # 保存報告
        with open('force_interpretation_report.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        # 打印報告
        for line in report:
            print(line)
        
        print(f"\n✅ 詳細報告已保存為 force_interpretation_report.txt")

def main():
    """主函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description='出力檢測結果解讀器')
    parser.add_argument('--input', '-i', type=str, 
                       default='force_analysis_result.csv',
                       help='出力檢測結果CSV檔案')
    
    args = parser.parse_args()
    
    # 創建解讀器
    interpreter = ForceAnalysisInterpreter()
    
    # 載入結果
    df = interpreter.load_analysis_result(args.input)
    
    # 分析各動作的出力模式
    action_analysis = interpreter.analyze_force_by_action(df)
    
    # 分析16顆螺絲的鎖緊模式
    screw_analysis = interpreter.analyze_screw_tightening_patterns(df)
    
    # 創建詳細時間軸
    interpreter.create_detailed_timeline(df)
    
    # 生成解讀報告
    interpreter.generate_interpretation_report(df, action_analysis, screw_analysis)

if __name__ == "__main__":
    main()
